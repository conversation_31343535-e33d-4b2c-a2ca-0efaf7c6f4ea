"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AgentController = void 0;
const express_1 = require("express");
const agent_service_1 = require("./agent.service");
const asyncHandler_1 = require("../utils/asyncHandler");
const errors_1 = require("../utils/errors");
const logger_1 = __importDefault(require("../config/logger"));
const type_guards_1 = require("../utils/type-guards");
const authorization_1 = require("../middleware/authorization");
const permissions_1 = require("../auth/permissions");
const agent_framework_1 = __importDefault(require("../agent-framework"));
class AgentController {
    constructor() {
        this.router = (0, express_1.Router)();
        this.agentService = new agent_service_1.AgentService();
        this.framework = agent_framework_1.default.getInstance();
        this.initializeRoutes();
    }
    initializeRoutes() {
        this.router.get('/', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.AGENT_READ]), (0, asyncHandler_1.asyncHandler)(this.getAllAgents));
        this.router.post('/', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.AGENT_CREATE]), (0, asyncHandler_1.asyncHandler)(this.createAgent));
        this.router.get('/:agentId', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.AGENT_READ]), (0, asyncHandler_1.asyncHandler)(this.getAgentById));
        this.router.put('/:agentId', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.AGENT_UPDATE]), (0, asyncHandler_1.asyncHandler)(this.updateAgent));
        this.router.delete('/:agentId', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.AGENT_DELETE]), (0, asyncHandler_1.asyncHandler)(this.deleteAgent));
        this.router.post('/:agentId/assign', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.AGENT_UPDATE]), (0, asyncHandler_1.asyncHandler)(this.assignAgent));
        this.router.post('/workflows/:workflowId/execute', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.WORKFLOW_CREATE]), (0, asyncHandler_1.asyncHandler)(this.executeWorkflow));
        this.router.get('/:agentId/performance', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.AGENT_READ]), (0, asyncHandler_1.asyncHandler)(this.getAgentPerformance));
        this.router.get('/framework/status', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.AGENT_READ]), (0, asyncHandler_1.asyncHandler)(this.getFrameworkStatus));
        this.router.post('/framework/execute', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.AGENT_UPDATE]), (0, asyncHandler_1.asyncHandler)(this.executeFrameworkTask));
        this.router.get('/framework/agents', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.AGENT_READ]), (0, asyncHandler_1.asyncHandler)(this.getFrameworkAgents));
        this.router.get('/framework/agents/:agentName', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.AGENT_READ]), (0, asyncHandler_1.asyncHandler)(this.getFrameworkAgent));
        this.router.post('/framework/agents/:agentName/execute', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.AGENT_UPDATE]), (0, asyncHandler_1.asyncHandler)(this.executeAgentTask));
    }
    async getAllAgents(req, res) {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const options = {
            page: parseInt((0, type_guards_1.getParam)(req.query.page, '1')),
            size: parseInt((0, type_guards_1.getParam)(req.query.size, '10')),
            sort: (0, type_guards_1.getParam)(req.query.sort),
            persona: (0, type_guards_1.getParam)(req.query.persona),
            status: (0, type_guards_1.getParam)(req.query.status),
        };
        const { agents, pagination } = await this.agentService.getAllAgents(req.user, options);
        res.status(200).json({ data: agents, pagination });
    }
    async createAgent(req, res) {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const createRequest = req.body;
        const newAgent = await this.agentService.createAgent(req.user, createRequest);
        res.status(201).json(newAgent);
    }
    async getAgentById(req, res) {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const { agentId } = req.params;
        const agent = await this.agentService.getAgentById(req.user, (0, type_guards_1.requireParam)(agentId, 'agentId'));
        if (!agent) {
            throw new errors_1.CustomError(`Agent with ID ${agentId} not found.`, { originalErrorCode: 'Not Found', originalStatusCode: 404 });
        }
        res.status(200).json(agent);
    }
    async updateAgent(req, res) {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const { agentId } = req.params;
        const updateRequest = req.body;
        const updatedAgent = await this.agentService.updateAgent(req.user, (0, type_guards_1.requireParam)(agentId, 'agentId'), updateRequest);
        if (!updatedAgent) {
            throw new errors_1.CustomError(`Agent with ID ${agentId} not found.`, { originalErrorCode: 'Not Found', originalStatusCode: 404 });
        }
        res.status(200).json(updatedAgent);
    }
    async deleteAgent(req, res) {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const { agentId } = req.params;
        const deleted = await this.agentService.deleteAgent(req.user, (0, type_guards_1.requireParam)(agentId, 'agentId'));
        if (!deleted) {
            throw new errors_1.CustomError(`Agent with ID ${agentId} not found.`, { originalErrorCode: 'Not Found', originalStatusCode: 404 });
        }
        res.status(204).send();
    }
    async assignAgent(req, res) {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const { agentId } = req.params;
        const assignmentRequest = req.body;
        const assignment = await this.agentService.assignAgent(req.user, { ...assignmentRequest, agentId: (0, type_guards_1.requireParam)(agentId, 'agentId') });
        res.status(200).json(assignment);
    }
    async executeWorkflow(req, res) {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const { workflowId } = req.params;
        const executionRequest = req.body;
        const executionStatus = await this.agentService.executeWorkflow(req.user, (0, type_guards_1.requireParam)(workflowId, 'workflowId'), executionRequest);
        res.status(200).json(executionStatus);
    }
    async getAgentPerformance(req, res) {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const { agentId } = req.params;
        const { startDate, endDate } = req.query;
        const performanceMetrics = await this.agentService.getAgentPerformance(req.user, (0, type_guards_1.requireParam)(agentId, 'agentId'), (0, type_guards_1.getParam)(startDate), (0, type_guards_1.getParam)(endDate));
        if (!performanceMetrics) {
            throw new errors_1.CustomError(`Performance metrics for agent with ID ${agentId} not found.`, { originalErrorCode: 'Not Found', originalStatusCode: 404 });
        }
        res.status(200).json(performanceMetrics);
    }
    async getFrameworkStatus(req, res) {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        try {
            const status = this.framework.getStatus();
            res.status(200).json({
                success: true,
                data: status,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            logger_1.default.error('Failed to get framework status:', error);
            throw new errors_1.CustomError('Failed to get framework status', { originalStatusCode: 500 });
        }
    }
    async executeFrameworkTask(req, res) {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const { taskType, payload, agentName } = req.body;
        if (!taskType) {
            throw new errors_1.CustomError('Task type is required', { originalStatusCode: 400 });
        }
        try {
            const result = await this.framework.executeTask(taskType, payload, agentName);
            res.status(200).json({
                success: true,
                data: result,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            logger_1.default.error('Framework task execution failed:', error);
            throw new errors_1.CustomError(error instanceof Error ? error.message : 'Task execution failed', { originalStatusCode: 500 });
        }
    }
    async getFrameworkAgents(req, res) {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        try {
            const predefinedAgents = this.framework.getPredefinedAgents();
            const agentList = Array.from(predefinedAgents.entries()).map(([name, agent]) => ({
                name,
                id: agent.id,
                type: agent.type,
                active: agent.getIsActive(),
                capabilities: agent.getCapabilities(),
                metrics: agent.getMetrics()
            }));
            res.status(200).json({
                success: true,
                data: agentList,
                count: agentList.length,
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            logger_1.default.error('Failed to get framework agents:', error);
            throw new errors_1.CustomError('Failed to get framework agents', { originalStatusCode: 500 });
        }
    }
    async getFrameworkAgent(req, res) {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const { agentName } = req.params;
        try {
            const agent = this.framework.getAgent((0, type_guards_1.requireParam)(agentName, 'agentName'));
            if (!agent) {
                throw new errors_1.CustomError(`Framework agent '${agentName}' not found`, { originalStatusCode: 404 });
            }
            res.status(200).json({
                success: true,
                data: {
                    name: agent.name,
                    id: agent.id,
                    type: agent.type,
                    active: agent.getIsActive(),
                    capabilities: agent.getCapabilities(),
                    metrics: agent.getMetrics(),
                    config: agent.getConfig().getAll()
                },
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            logger_1.default.error(`Failed to get framework agent '${agentName}':`, error);
            if (error instanceof errors_1.CustomError) {
                throw error;
            }
            throw new errors_1.CustomError('Failed to get framework agent', { originalStatusCode: 500 });
        }
    }
    async executeAgentTask(req, res) {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const { agentName } = req.params;
        const { taskType, payload } = req.body;
        if (!taskType) {
            throw new errors_1.CustomError('Task type is required', { originalStatusCode: 400 });
        }
        try {
            const agent = this.framework.getAgent((0, type_guards_1.requireParam)(agentName, 'agentName'));
            if (!agent) {
                throw new errors_1.CustomError(`Framework agent '${agentName}' not found`, { originalStatusCode: 404 });
            }
            const task = {
                id: `task_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
                type: taskType,
                payload: payload || {},
                timestamp: new Date()
            };
            const result = await agent.execute(task);
            res.status(200).json({
                success: true,
                data: result,
                agent: {
                    name: agent.name,
                    id: agent.id,
                    type: agent.type
                },
                timestamp: new Date().toISOString()
            });
        }
        catch (error) {
            logger_1.default.error(`Failed to execute task on agent '${agentName}':`, error);
            if (error instanceof errors_1.CustomError) {
                throw error;
            }
            throw new errors_1.CustomError('Failed to execute agent task', { originalStatusCode: 500 });
        }
    }
}
exports.AgentController = AgentController;
