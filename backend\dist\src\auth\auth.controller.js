"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const auth_service_1 = require("./auth.service");
const validation_1 = require("../utils/validation");
const asyncHandler_1 = require("../utils/asyncHandler");
const joi_1 = __importDefault(require("joi"));
const authService = new auth_service_1.AuthService();
const router = (0, express_1.Router)();
const registerSchema = joi_1.default.object({
    email: joi_1.default.string().email().required().messages({
        'string.email': 'Invalid email format.',
        'string.empty': 'Email is required.',
        'any.required': 'Email is required.',
    }),
    password: joi_1.default.string().min(8).required().messages({
        'string.min': 'Password must be at least 8 characters long.',
        'string.empty': 'Password is required.',
        'any.required': 'Password is required.',
    }),
    firstName: joi_1.default.string().required().messages({
        'string.empty': 'First name is required.',
        'any.required': 'First name is required.',
    }),
    lastName: joi_1.default.string().required().messages({
        'string.empty': 'Last name is required.',
        'any.required': 'Last name is required.',
    }),
});
const loginSchema = joi_1.default.object({
    email: joi_1.default.string().email().required().messages({
        'string.email': 'Invalid email format.',
        'string.empty': 'Email is required.',
        'any.required': 'Email is required.',
    }),
    password: joi_1.default.string().required().messages({
        'string.empty': 'Password is required.',
        'any.required': 'Password is required.',
    }),
});
router.post('/register', (0, validation_1.validate)(registerSchema), (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const userData = req.body;
    const newUser = await authService.register(userData);
    res.status(201).json(newUser);
}));
router.post('/login', (0, validation_1.validate)(loginSchema), (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const loginData = req.body;
    const authToken = await authService.login(loginData);
    res.status(200).json(authToken);
}));
router.post('/refresh-token', (0, asyncHandler_1.asyncHandler)(async (req, res) => {
    const { refreshToken } = req.body;
    if (!refreshToken) {
        return res.status(400).json({ message: 'Refresh token is required.' });
    }
    const newAuthToken = await authService.refreshToken(refreshToken);
    return res.status(200).json(newAuthToken);
}));
exports.default = router;
