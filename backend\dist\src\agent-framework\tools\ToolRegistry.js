"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ToolRegistry = exports.Tool = void 0;
const logger_1 = __importDefault(require("../../config/logger"));
class Tool {
    constructor(definition) {
        this.definition = definition;
    }
    async execute(parameters, context) {
        const startTime = Date.now();
        try {
            this.validateParameters(parameters);
            const result = await this.onExecute(parameters, context);
            const executionTime = Date.now() - startTime;
            return {
                success: true,
                data: result,
                executionTime,
                toolId: this.definition.id,
                context
            };
        }
        catch (error) {
            const executionTime = Date.now() - startTime;
            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error',
                executionTime,
                toolId: this.definition.id,
                context
            };
        }
    }
    validateParameters(parameters) {
        for (const param of this.definition.parameters) {
            const value = parameters[param.name];
            if (param.required && (value === undefined || value === null)) {
                throw new Error(`Required parameter '${param.name}' is missing`);
            }
            if (value === undefined || value === null) {
                continue;
            }
            if (!this.validateParameterType(value, param.type)) {
                throw new Error(`Parameter '${param.name}' must be of type ${param.type}`);
            }
            if (param.validation) {
                this.validateParameterConstraints(value, param);
            }
        }
    }
    validateParameterType(value, expectedType) {
        switch (expectedType) {
            case 'string':
                return typeof value === 'string';
            case 'number':
                return typeof value === 'number' && !isNaN(value);
            case 'boolean':
                return typeof value === 'boolean';
            case 'object':
                return typeof value === 'object' && value !== null && !Array.isArray(value);
            case 'array':
                return Array.isArray(value);
            default:
                return true;
        }
    }
    validateParameterConstraints(value, param) {
        const validation = param.validation;
        if (typeof value === 'number') {
            if (validation.min !== undefined && value < validation.min) {
                throw new Error(`Parameter '${param.name}' must be >= ${validation.min}`);
            }
            if (validation.max !== undefined && value > validation.max) {
                throw new Error(`Parameter '${param.name}' must be <= ${validation.max}`);
            }
        }
        if (typeof value === 'string') {
            if (validation.pattern) {
                const regex = new RegExp(validation.pattern);
                if (!regex.test(value)) {
                    throw new Error(`Parameter '${param.name}' does not match required pattern`);
                }
            }
        }
        if (validation.enum && !validation.enum.includes(value)) {
            throw new Error(`Parameter '${param.name}' must be one of: ${validation.enum.join(', ')}`);
        }
    }
}
exports.Tool = Tool;
class ToolRegistry {
    constructor() {
        this.tools = new Map();
        this.categories = new Map();
        this.dependencies = new Map();
    }
    static getInstance() {
        if (!ToolRegistry.instance) {
            ToolRegistry.instance = new ToolRegistry();
        }
        return ToolRegistry.instance;
    }
    register(tool) {
        const definition = tool.definition;
        if (this.tools.has(definition.id)) {
            throw new Error(`Tool with ID '${definition.id}' is already registered`);
        }
        this.validateDependencies(definition.dependencies);
        this.tools.set(definition.id, tool);
        if (!this.categories.has(definition.category)) {
            this.categories.set(definition.category, []);
        }
        this.categories.get(definition.category).push(definition.id);
        if (definition.dependencies.length > 0) {
            this.dependencies.set(definition.id, definition.dependencies);
        }
        logger_1.default.info(`Tool '${definition.name}' (${definition.id}) registered`);
    }
    unregister(toolId) {
        const tool = this.tools.get(toolId);
        if (!tool) {
            return false;
        }
        const dependents = this.findDependents(toolId);
        if (dependents.length > 0) {
            throw new Error(`Cannot unregister tool '${toolId}': it is required by ${dependents.join(', ')}`);
        }
        this.tools.delete(toolId);
        const category = tool.definition.category;
        const categoryTools = this.categories.get(category);
        if (categoryTools) {
            const index = categoryTools.indexOf(toolId);
            if (index !== -1) {
                categoryTools.splice(index, 1);
                if (categoryTools.length === 0) {
                    this.categories.delete(category);
                }
            }
        }
        this.dependencies.delete(toolId);
        logger_1.default.info(`Tool '${toolId}' unregistered`);
        return true;
    }
    get(toolId) {
        return this.tools.get(toolId);
    }
    getAll() {
        return Array.from(this.tools.values());
    }
    getByCategory(category) {
        const toolIds = this.categories.get(category) || [];
        return toolIds.map(id => this.tools.get(id)).filter(Boolean);
    }
    getCategories() {
        return Array.from(this.categories.keys());
    }
    search(query) {
        const lowerQuery = query.toLowerCase();
        return Array.from(this.tools.values()).filter(tool => tool.definition.name.toLowerCase().includes(lowerQuery) ||
            tool.definition.description.toLowerCase().includes(lowerQuery));
    }
    getDefinition(toolId) {
        const tool = this.tools.get(toolId);
        return tool ? tool.definition : undefined;
    }
    getAllDefinitions() {
        return Array.from(this.tools.values()).map(tool => tool.definition);
    }
    isAvailable(toolId) {
        const tool = this.tools.get(toolId);
        return tool ? tool.definition.enabled : false;
    }
    setEnabled(toolId, enabled) {
        const tool = this.tools.get(toolId);
        if (!tool) {
            throw new Error(`Tool '${toolId}' not found`);
        }
        tool.definition.enabled = enabled;
        logger_1.default.info(`Tool '${toolId}' ${enabled ? 'enabled' : 'disabled'}`);
    }
    async execute(toolId, parameters, context) {
        const tool = this.tools.get(toolId);
        if (!tool) {
            throw new Error(`Tool '${toolId}' not found`);
        }
        if (!tool.definition.enabled) {
            throw new Error(`Tool '${toolId}' is disabled`);
        }
        logger_1.default.info(`Executing tool '${toolId}' for agent ${context.agentId}`);
        return await tool.execute(parameters, context);
    }
    validateDependencies(dependencies) {
        for (const dep of dependencies) {
            if (!this.tools.has(dep)) {
                throw new Error(`Dependency '${dep}' is not registered`);
            }
        }
    }
    findDependents(toolId) {
        const dependents = [];
        for (const [id, deps] of this.dependencies.entries()) {
            if (deps.includes(toolId)) {
                dependents.push(id);
            }
        }
        return dependents;
    }
    getStats() {
        const allTools = Array.from(this.tools.values());
        const enabledTools = allTools.filter(tool => tool.definition.enabled);
        const toolsByCategory = {};
        for (const [category, toolIds] of this.categories.entries()) {
            toolsByCategory[category] = toolIds.length;
        }
        return {
            totalTools: allTools.length,
            enabledTools: enabledTools.length,
            categories: this.categories.size,
            toolsByCategory
        };
    }
    clear() {
        this.tools.clear();
        this.categories.clear();
        this.dependencies.clear();
        logger_1.default.info('Tool registry cleared');
    }
}
exports.ToolRegistry = ToolRegistry;
