"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.McpRepository = void 0;
class McpRepository {
    constructor(knex) {
        this.knex = knex;
    }
    async createMcpServer(server) {
        const [newServer] = await this.knex('mcp_servers').insert(server).returning('*');
        return newServer;
    }
    async getMcpServerById(id) {
        return this.knex('mcp_servers').where({ id }).first();
    }
    async getMcpServerByBaseUrl(baseUrl) {
        return this.knex('mcp_servers').where({ baseUrl }).first();
    }
    async listMcpServers() {
        return this.knex('mcp_servers').select('*');
    }
    async updateMcpServer(id, updates) {
        const [updatedServer] = await this.knex('mcp_servers').where({ id }).update(updates).returning('*');
        return updatedServer;
    }
    async deleteMcpServer(id) {
        const deletedCount = await this.knex('mcp_servers').where({ id }).del();
        return deletedCount > 0;
    }
    async createMcpCapability(capability) {
        const [newCapability] = await this.knex('mcp_capabilities').insert(capability).returning('*');
        return newCapability;
    }
    async getMcpCapabilityById(id) {
        return this.knex('mcp_capabilities').where({ id }).first();
    }
    async listMcpCapabilitiesByServerId(serverId) {
        return this.knex('mcp_capabilities').where({ serverId }).select('*');
    }
    async updateMcpCapability(id, updates) {
        const [updatedCapability] = await this.knex('mcp_capabilities').where({ id }).update(updates).returning('*');
        return updatedCapability;
    }
    async deleteMcpCapability(id) {
        const deletedCount = await this.knex('mcp_capabilities').where({ id }).del();
        return deletedCount > 0;
    }
    async createMcpRequestLog(log) {
        const [newLog] = await this.knex('mcp_request_logs').insert(log).returning('*');
        return newLog;
    }
    async listMcpRequestLogs(filters) {
        let query = this.knex('mcp_request_logs').select('*');
        if (filters.serverId)
            query = query.where({ serverId: filters.serverId });
        if (filters.capabilityId)
            query = query.where({ capabilityId: filters.capabilityId });
        if (filters.status)
            query = query.where({ status: filters.status });
        return query;
    }
    async createMcpMetric(metric) {
        const [newMetric] = await this.knex('mcp_metrics').insert(metric).returning('*');
        return newMetric;
    }
    async listMcpMetrics(filters) {
        let query = this.knex('mcp_metrics').select('*');
        if (filters.serverId)
            query = query.where({ serverId: filters.serverId });
        if (filters.metricType)
            query = query.where({ metricType: filters.metricType });
        if (filters.from)
            query = query.where('timestamp', '>=', filters.from);
        if (filters.to)
            query = query.where('timestamp', '<=', filters.to);
        return query;
    }
    async createMcpConfiguration(config) {
        const [newConfig] = await this.knex('mcp_configurations').insert(config).returning('*');
        return newConfig;
    }
    async getMcpConfigurationByKey(key) {
        return this.knex('mcp_configurations').where({ key }).first();
    }
    async updateMcpConfiguration(key, updates) {
        const [updatedConfig] = await this.knex('mcp_configurations').where({ key }).update(updates).returning('*');
        return updatedConfig;
    }
    async deleteMcpConfiguration(key) {
        const deletedCount = await this.knex('mcp_configurations').where({ key }).del();
        return deletedCount > 0;
    }
    async createMcpAccessControl(accessControl) {
        const [newAccessControl] = await this.knex('mcp_access_controls').insert(accessControl).returning('*');
        return newAccessControl;
    }
    async listMcpAccessControls(filters) {
        let query = this.knex('mcp_access_controls').select('*');
        if (filters.serverId)
            query = query.where({ serverId: filters.serverId });
        if (filters.paimHierarchyId)
            query = query.where({ paimHierarchyId: filters.paimHierarchyId });
        return query;
    }
    async createMcpRateLimit(rateLimit) {
        const [newRateLimit] = await this.knex('mcp_rate_limits').insert(rateLimit).returning('*');
        return newRateLimit;
    }
    async getMcpRateLimit(serverId, paimHierarchyId) {
        let query = this.knex('mcp_rate_limits').where({ serverId });
        if (paimHierarchyId)
            query = query.where({ paimHierarchyId });
        return query.first();
    }
    async updateMcpRateLimit(id, updates) {
        const [updatedRateLimit] = await this.knex('mcp_rate_limits').where({ id }).update(updates).returning('*');
        return updatedRateLimit;
    }
    async createMcpQuota(quota) {
        const [newQuota] = await this.knex('mcp_quotas').insert(quota).returning('*');
        return newQuota;
    }
    async getMcpQuota(serverId, paimHierarchyId) {
        let query = this.knex('mcp_quotas').where({ serverId });
        if (paimHierarchyId)
            query = query.where({ paimHierarchyId });
        return query.first();
    }
    async updateMcpQuota(id, updates) {
        const [updatedQuota] = await this.knex('mcp_quotas').where({ id }).update(updates).returning('*');
        return updatedQuota;
    }
    async createMcpNotification(notification) {
        const [newNotification] = await this.knex('mcp_notifications').insert(notification).returning('*');
        return newNotification;
    }
    async listMcpNotifications(filters) {
        let query = this.knex('mcp_notifications').select('*');
        if (filters.serverId)
            query = query.where({ serverId: filters.serverId });
        if (filters.type)
            query = query.where({ type: filters.type });
        if (typeof filters.read === 'boolean')
            query = query.where({ read: filters.read });
        return query;
    }
    async markMcpNotificationAsRead(id) {
        const [updatedNotification] = await this.knex('mcp_notifications').where({ id }).update({ read: true }).returning('*');
        return updatedNotification;
    }
    async createMcpAuditLog(auditLog) {
        const [newAuditLog] = await this.knex('mcp_audit_logs').insert(auditLog).returning('*');
        return newAuditLog;
    }
    async listMcpAuditLogs(filters) {
        let query = this.knex('mcp_audit_logs').select('*');
        if (filters.serverId)
            query = query.where({ serverId: filters.serverId });
        if (filters.userId)
            query = query.where({ userId: filters.userId });
        if (filters.action)
            query = query.where({ action: filters.action });
        if (filters.from)
            query = query.where('timestamp', '>=', filters.from);
        if (filters.to)
            query = query.where('timestamp', '<=', filters.to);
        return query;
    }
    async checkUserAccess(serverId, userId) {
        return true;
    }
}
exports.McpRepository = McpRepository;
