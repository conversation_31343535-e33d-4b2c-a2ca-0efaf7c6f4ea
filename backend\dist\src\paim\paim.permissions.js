"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PAIM_TIER_PERMISSIONS = exports.PAIMTier = void 0;
exports.tierIncludes = tierIncludes;
const permissions_1 = require("../auth/permissions");
const db_1 = require("../types/db");
Object.defineProperty(exports, "PAIMTier", { enumerable: true, get: function () { return db_1.PaimTierEnum; } });
const basicPermissions = [
    permissions_1.PERMISSIONS.PAIM_READ,
    permissions_1.PERMISSIONS.POWER_OPS_READ,
    permissions_1.PERMISSIONS.WORKFLOW_READ,
    permissions_1.PERMISSIONS.AGENT_READ,
];
const professionalPermissions = [
    ...basicPermissions,
    permissions_1.PERMISSIONS.PAIM_CREATE,
    permissions_1.PERMISSIONS.PAIM_UPDATE,
    permissions_1.PERMISSIONS.POWER_OPS_CREATE,
    permissions_1.PERMISSIONS.POWER_OPS_UPDATE,
    permissions_1.PERMISSIONS.WORKFLOW_CREATE,
    permissions_1.PERMISSIONS.WORKFLOW_UPDATE,
];
const enterprisePermissions = [
    ...professionalPermissions,
    permissions_1.PERMISSIONS.PAIM_DELETE,
    permissions_1.PERMISSIONS.PAIM_MANAGE_TIERS,
    permissions_1.PERMISSIONS.POWER_OPS_DELETE,
    permissions_1.PERMISSIONS.POWER_OPS_MANAGE_ALL,
    permissions_1.PERMISSIONS.WORKFLOW_DELETE,
    permissions_1.PERMISSIONS.WORKFLOW_MANAGE_ALL,
    permissions_1.PERMISSIONS.AGENT_CREATE,
    permissions_1.PERMISSIONS.AGENT_UPDATE,
    permissions_1.PERMISSIONS.AGENT_DELETE,
    permissions_1.PERMISSIONS.AGENT_MANAGE_ALL,
    permissions_1.PERMISSIONS.ORG_MANAGE_MEMBERS,
    permissions_1.PERMISSIONS.ORG_MANAGE_ROLES,
    permissions_1.PERMISSIONS.BILLING_VIEW,
];
exports.PAIM_TIER_PERMISSIONS = {
    [db_1.PaimTierEnum.Basic]: basicPermissions,
    [db_1.PaimTierEnum.Professional]: professionalPermissions,
    [db_1.PaimTierEnum.Enterprise]: enterprisePermissions,
    [db_1.PaimTierEnum.Custom]: [
        ...Object.values(permissions_1.PERMISSIONS),
    ],
};
function tierIncludes(tier, requiredTier) {
    const tierOrder = [db_1.PaimTierEnum.Basic, db_1.PaimTierEnum.Professional, db_1.PaimTierEnum.Enterprise, db_1.PaimTierEnum.Custom];
    const tierIndex = tierOrder.indexOf(tier);
    const requiredTierIndex = tierOrder.indexOf(requiredTier);
    return tierIndex >= requiredTierIndex;
}
