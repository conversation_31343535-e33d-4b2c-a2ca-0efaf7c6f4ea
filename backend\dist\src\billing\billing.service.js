"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BillingService = void 0;
const audit_service_1 = require("../audit/audit.service");
const audit_types_1 = require("../audit/audit.types");
const logger_1 = __importDefault(require("../config/logger"));
const authorization_service_1 = require("../auth/authorization.service");
const permissions_1 = require("../auth/permissions");
const errors_1 = require("../utils/errors");
class BillingService {
    constructor(billingRepository) {
        this.POWEROP_COST_USD = 0.08;
        this.billingRepository = billingRepository;
        this.auditTrailService = new audit_service_1.AuditTrailService();
    }
    async trackPowerOpsUsage(user, org_id, powerops_used) {
        logger_1.default.info(`Attempting to track PowerOps usage for organization ${org_id} with ${powerops_used} PowerOps by user ${user.userId}.`);
        if (!authorization_service_1.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.BILLING_MANAGE)) {
            throw new errors_1.AuthorizationError('Forbidden: Insufficient permissions to track PowerOps usage.', 403);
        }
        if (user.tenantId !== org_id && !authorization_service_1.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.SYSTEM_ADMIN_ACCESS)) {
            throw new errors_1.AuthorizationError('Forbidden: You can only track usage for your own organization.', 403);
        }
        const cost_usd = powerops_used * this.POWEROP_COST_USD;
        const data = {
            org_id,
            powerops_used,
            cost_usd,
            month: new Date().toISOString().slice(0, 7) + '-01'
        };
        const record = await this.billingRepository.insertPowerOpsUsage(data);
        logger_1.default.info(`Successfully tracked PowerOps usage for organization ${org_id}. Record ID: ${record.id}`);
        await this.auditTrailService.logEvent({
            tenantId: org_id,
            userId: user.userId,
            category: audit_types_1.AuditEventCategory.BILLING,
            operationType: 'POWER_OPS_USAGE_TRACKED',
            description: `PowerOps usage tracked for organization ${org_id}: ${powerops_used} PowerOps, Cost: $${cost_usd.toFixed(2)}`,
            severity: audit_types_1.AuditEventSeverity.INFO,
            timestamp: new Date(),
            metadata: { powerops_used, cost_usd },
        });
        logger_1.default.info(`STRIPE DISABLED: Would have created invoice line item for ${org_id} with cost $${cost_usd.toFixed(2)}`);
        return record;
    }
    async createInvoiceLineItem(customer_id, amount, description, currency = 'usd') {
        logger_1.default.info(`STRIPE DISABLED: Would have created invoice item for customer ${customer_id}: Amount ${amount / 100} ${currency}, Description: ${description}`);
        await this.auditTrailService.logEvent({
            tenantId: customer_id,
            category: audit_types_1.AuditEventCategory.BILLING,
            operationType: 'STRIPE_DISABLED_INVOICE_ITEM_SKIPPED',
            description: `Stripe disabled: Would have created invoice item for customer ${customer_id}: Amount ${amount / 100} ${currency}, Description: ${description}`,
            severity: audit_types_1.AuditEventSeverity.INFO,
            timestamp: new Date(),
            metadata: { customer_id, amount, currency, description, stripe_disabled: true },
        });
        return {
            id: `mock_ii_${Date.now()}`,
            object: 'invoiceitem',
            amount: amount,
            currency: currency,
            customer: customer_id,
            description: description,
            stripe_disabled: true
        };
    }
}
exports.BillingService = BillingService;
