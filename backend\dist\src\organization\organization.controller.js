"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.OrganizationController = void 0;
const express_1 = require("express");
const asyncHandler_1 = require("../utils/asyncHandler");
const validation_1 = require("../utils/validation");
const joi_1 = __importDefault(require("joi"));
const auth_middleware_1 = require("../auth/auth.middleware");
const auth_middleware_2 = require("../auth/auth.middleware");
const db_1 = require("../types/db");
const type_guards_1 = require("../utils/type-guards");
const UpdateSeatsSchema = joi_1.default.object({
    org_seats_allowed: joi_1.default.number().integer().min(0).required(),
});
const AssignSeatSchema = joi_1.default.object({
    org_id: joi_1.default.string().guid().required(),
    user_id: joi_1.default.string().guid().required(),
});
const RevokeSeatSchema = joi_1.default.object({
    org_id: joi_1.default.string().guid().required(),
    user_id: joi_1.default.string().guid().required(),
});
class OrganizationController {
    constructor(organizationService) {
        this.organizationService = organizationService;
        this.router = (0, express_1.Router)();
        this.initializeRoutes();
    }
    initializeRoutes() {
        this.router.put('/:id/seats', auth_middleware_1.authenticate, (0, auth_middleware_2.authorizePaimTier)(db_1.PaimTierEnum.CompanyAdmin), (0, validation_1.validate)(UpdateSeatsSchema), (0, asyncHandler_1.asyncHandler)(this.updateOrganizationSeats.bind(this)));
        this.router.get('/:id/seats', auth_middleware_1.authenticate, (0, auth_middleware_2.authorizePaimTier)(db_1.PaimTierEnum.CompanyAdmin), (0, asyncHandler_1.asyncHandler)(this.getOrganizationSeats.bind(this)));
        this.router.post('/assign', auth_middleware_1.authenticate, (0, auth_middleware_2.authorizePaimTier)(db_1.PaimTierEnum.CompanyAdmin), (0, validation_1.validate)(AssignSeatSchema), (0, asyncHandler_1.asyncHandler)(this.assignSeat.bind(this)));
        this.router.post('/revoke', auth_middleware_1.authenticate, (0, auth_middleware_2.authorizePaimTier)(db_1.PaimTierEnum.CompanyAdmin), (0, validation_1.validate)(RevokeSeatSchema), (0, asyncHandler_1.asyncHandler)(this.revokeSeat.bind(this)));
    }
    async updateOrganizationSeats(req, res) {
        const id = (0, type_guards_1.requireParam)(req.params.id, 'id');
        const { org_seats_allowed } = req.body;
        try {
            const updatedSeats = await this.organizationService.updateAllowedSeats(id, org_seats_allowed);
            res.status(200).json({ message: 'Organization seats updated successfully', data: updatedSeats });
        }
        catch (error) {
            res.status(400).json({ message: error.message });
        }
    }
    async getOrganizationSeats(req, res) {
        const id = (0, type_guards_1.requireParam)(req.params.id, 'id');
        try {
            const organizationSeats = await this.organizationService.getOrganizationSeats(id);
            if (!organizationSeats) {
                return res.status(404).json({ message: 'Organization not found.' });
            }
            return res.status(200).json({ message: 'Organization seats retrieved successfully', data: organizationSeats });
        }
        catch (error) {
            return res.status(500).json({ message: error.message });
        }
    }
    async assignSeat(req, res) {
        const { org_id, user_id } = req.body;
        try {
            const updatedSeats = await this.organizationService.assignSeat(org_id, user_id);
            res.status(200).json({ message: 'Seat assigned successfully', data: updatedSeats });
        }
        catch (error) {
            res.status(400).json({ message: error.message });
        }
    }
    async revokeSeat(req, res) {
        const { org_id, user_id } = req.body;
        try {
            const updatedSeats = await this.organizationService.revokeSeat(org_id, user_id);
            res.status(200).json({ message: 'Seat revoked successfully', data: updatedSeats });
        }
        catch (error) {
            res.status(400).json({ message: error.message });
        }
    }
}
exports.OrganizationController = OrganizationController;
