"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowCollaborationService = void 0;
const errors_1 = require("../utils/errors");
class WorkflowCollaborationService {
    constructor(workflowCollaborationRepository, collaborationEvents, collaborationSessionService) {
        this.workflowCollaborationRepository = workflowCollaborationRepository;
        this.collaborationEvents = collaborationEvents;
        this.collaborationSessionService = collaborationSessionService;
    }
    async getAllWorkflows(status, page = 1, size = 10, sort) {
        const filters = { page, size };
        if (status !== undefined)
            filters.status = status;
        if (sort !== undefined)
            filters.sort = sort;
        const { workflows, total } = await this.workflowCollaborationRepository.getWorkflows(filters);
        return { data: workflows, pagination: { total, page, size } };
    }
    async createWorkflow(workflowData) {
        const newWorkflow = await this.workflowCollaborationRepository.createWorkflow(workflowData);
        this.collaborationEvents.emitWorkflowUpdate({
            workflowId: newWorkflow.id,
        });
        return newWorkflow;
    }
    async getWorkflowById(workflowId) {
        const workflow = await this.workflowCollaborationRepository.getWorkflowById(workflowId);
        if (!workflow) {
            throw new errors_1.CustomError('Workflow not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
        }
        return workflow;
    }
    async updateWorkflow(workflowId, workflowData) {
        const updatedWorkflow = await this.workflowCollaborationRepository.updateWorkflow(workflowId, workflowData);
        if (!updatedWorkflow) {
            throw new errors_1.CustomError('Workflow not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
        }
        this.collaborationEvents.emitWorkflowUpdate({
            workflowId: updatedWorkflow.id,
            status: updatedWorkflow.status,
        });
        this.collaborationSessionService.synchronizeWorkflowState(updatedWorkflow.id, updatedWorkflow);
        return updatedWorkflow;
    }
    async deleteWorkflow(workflowId) {
        const deleted = await this.workflowCollaborationRepository.deleteWorkflow(workflowId);
        if (!deleted) {
            throw new errors_1.CustomError('Workflow not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
        }
    }
    async getAllTasks(status, assignedTo, page = 1, size = 10, sort) {
        const filters = { page, size };
        if (status !== undefined)
            filters.status = status;
        if (assignedTo !== undefined)
            filters.assignedTo = assignedTo;
        if (sort !== undefined)
            filters.sort = sort;
        const { tasks, total } = await this.workflowCollaborationRepository.getTasks(filters);
        return { data: tasks, pagination: { total, page, size } };
    }
    async createTask(taskData) {
        const newTask = await this.workflowCollaborationRepository.createTask(taskData);
        if (newTask.workflowId) {
            this.collaborationEvents.emitWorkflowUpdate({
                workflowId: newTask.workflowId,
                taskId: newTask.id,
                status: newTask.status,
                assignedToId: newTask.assignedToId,
            });
        }
        else {
            console.warn(`Task ${newTask.id} created without workflowId, skipping WebSocket update.`);
        }
        return newTask;
    }
    async getTaskById(taskId) {
        const task = await this.workflowCollaborationRepository.getTaskById(taskId);
        if (!task) {
            throw new errors_1.CustomError('Task not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
        }
        return task;
    }
    async updateTask(taskId, taskData) {
        const updatedTask = await this.workflowCollaborationRepository.updateTask(taskId, taskData);
        if (!updatedTask) {
            throw new errors_1.CustomError('Task not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
        }
        if (updatedTask.workflowId) {
            this.collaborationEvents.emitWorkflowUpdate({
                workflowId: updatedTask.workflowId,
                taskId: updatedTask.id,
                status: updatedTask.status,
                assignedToId: updatedTask.assignedToId,
            });
        }
        else {
            console.warn(`Task ${updatedTask.id} updated without workflowId, skipping WebSocket update.`);
        }
        return updatedTask;
    }
    async deleteTask(taskId) {
        const deleted = await this.workflowCollaborationRepository.deleteTask(taskId);
        if (!deleted) {
            throw new errors_1.CustomError('Task not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
        }
    }
    async startCollaborationSession(sessionData) {
        const newSession = await this.workflowCollaborationRepository.createCollaborationSession(sessionData);
        return newSession;
    }
    async joinCollaborationSession(sessionId, userId) {
        const updatedSession = await this.workflowCollaborationRepository.addParticipantToSession(sessionId, userId);
        if (!updatedSession) {
            throw new errors_1.CustomError('Collaboration session not found or user already joined', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
        }
        return updatedSession;
    }
    async leaveCollaborationSession(sessionId, userId) {
        const left = await this.workflowCollaborationRepository.removeParticipantFromSession(sessionId, userId);
        if (!left) {
            throw new errors_1.CustomError('Collaboration session not found or user not in session', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
        }
    }
    async sendCrossTenantMessage(messageData) {
        const result = await this.workflowCollaborationRepository.createCrossTenantMessage(messageData);
        return result;
    }
    async getAllNotifications(userId, read, page = 1, size = 10, sort) {
        const filters = { page, size };
        if (userId !== undefined)
            filters.userId = userId;
        if (read !== undefined) {
            filters.read = read === 'true';
        }
        if (sort !== undefined)
            filters.sort = sort;
        const { notifications, total } = await this.workflowCollaborationRepository.getNotifications(filters);
        return { data: notifications, pagination: { total, page, size } };
    }
    async markNotificationAsRead(notificationId) {
        const marked = await this.workflowCollaborationRepository.markNotificationAsRead(notificationId);
        if (!marked) {
            throw new errors_1.CustomError('Notification not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
        }
    }
    async shareWorkflow(shareData) {
        const result = await this.workflowCollaborationRepository.shareWorkflow(shareData);
        return result;
    }
    async deleteWorkflowShare(workflowId, permissionId) {
        const deleted = await this.workflowCollaborationRepository.deleteWorkflowShare(workflowId, permissionId);
        if (!deleted) {
            throw new errors_1.CustomError('Workflow share not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
        }
    }
    async delegateTask(taskId, delegateData) {
        const result = await this.workflowCollaborationRepository.delegateTask(taskId, delegateData);
        return result;
    }
    async getAllWorkspaces(ownerId, paimInstanceId, page = 1, size = 10, sort) {
        const filters = { page, size };
        if (ownerId !== undefined)
            filters.ownerId = ownerId;
        if (paimInstanceId !== undefined)
            filters.paimInstanceId = paimInstanceId;
        if (sort !== undefined)
            filters.sort = sort;
        const { workspaces, total } = await this.workflowCollaborationRepository.getWorkspaces(filters);
        return { data: workspaces, pagination: { total, page, size } };
    }
    async createWorkspace(workspaceData) {
        const newWorkspace = await this.workflowCollaborationRepository.createWorkspace(workspaceData);
        return newWorkspace;
    }
    async getWorkspaceById(workspaceId) {
        const workspace = await this.workflowCollaborationRepository.getWorkspaceById(workspaceId);
        if (!workspace) {
            throw new errors_1.CustomError('Workspace not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
        }
        return workspace;
    }
    async updateWorkspace(workspaceId, workspaceData) {
        const updatedWorkspace = await this.workflowCollaborationRepository.updateWorkspace(workspaceId, workspaceData);
        if (!updatedWorkspace) {
            throw new errors_1.CustomError('Workspace not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
        }
        return updatedWorkspace;
    }
    async deleteWorkspace(workspaceId) {
        const deleted = await this.workflowCollaborationRepository.deleteWorkspace(workspaceId);
        if (!deleted) {
            throw new errors_1.CustomError('Workspace not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
        }
    }
    async getAllTeams(paimInstanceId, page = 1, size = 10, sort) {
        const filters = { page, size };
        if (paimInstanceId !== undefined)
            filters.paimInstanceId = paimInstanceId;
        if (sort !== undefined)
            filters.sort = sort;
        const { teams, total } = await this.workflowCollaborationRepository.getTeams(filters);
        return { data: teams, pagination: { total, page, size } };
    }
    async createTeam(teamData) {
        const newTeam = await this.workflowCollaborationRepository.createTeam(teamData);
        return newTeam;
    }
    async getTeamById(teamId) {
        const team = await this.workflowCollaborationRepository.getTeamById(teamId);
        if (!team) {
            throw new errors_1.CustomError('Team not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
        }
        return team;
    }
    async updateTeam(teamId, teamData) {
        const updatedTeam = await this.workflowCollaborationRepository.updateTeam(teamId, teamData);
        if (!updatedTeam) {
            throw new errors_1.CustomError('Team not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
        }
        return updatedTeam;
    }
    async deleteTeam(teamId) {
        const deleted = await this.workflowCollaborationRepository.deleteTeam(teamId);
        if (!deleted) {
            throw new errors_1.CustomError('Team not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
        }
    }
}
exports.WorkflowCollaborationService = WorkflowCollaborationService;
