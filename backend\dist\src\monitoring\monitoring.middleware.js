"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MonitoringMiddleware = void 0;
const monitoring_service_1 = require("./monitoring.service");
const monitoring_repository_1 = require("./monitoring.repository");
const audit_service_1 = require("../audit/audit.service");
const paim_service_1 = require("../paim/paim.service");
const powerops_service_1 = require("../powerops/powerops.service");
const cultural_sensitivity_service_1 = require("../cultural-sensitivity/cultural-sensitivity.service");
const db_1 = __importDefault(require("../database/db"));
const logger_1 = __importDefault(require("../config/logger"));
const monitoring_types_1 = require("./monitoring.types");
const uuid_1 = require("uuid");
class MonitoringMiddleware {
    constructor() {
        this.requestMonitor = async (req, res, next) => {
            const start = process.hrtime.bigint();
            res.on('finish', async () => {
                const end = process.hrtime.bigint();
                const duration = Number(end - start) / 1000000;
                const serviceName = req.baseUrl.split('/')[2] || 'unknown-service';
                const endpoint = `${req.method} ${req.originalUrl}`;
                const statusCode = res.statusCode;
                logger_1.default.info(`Request: ${endpoint}, Status: ${statusCode}, Duration: ${duration}ms`);
                const metrics = {
                    cpuUsage: 0,
                    memoryUsage: 0,
                    diskUsage: 0,
                    networkLatency: 0,
                    requestPerSecond: 1,
                    errorRate: statusCode >= 400 ? 1 : 0,
                    responseTime: duration,
                };
                try {
                    await this.monitoringService.recordPerformanceMetrics(serviceName, metrics);
                }
                catch (error) {
                    logger_1.default.error(`Failed to record performance metrics: ${error}`);
                }
            });
            next();
        };
        this.errorTracker = async (err, req, res, next) => {
            logger_1.default.error(`Unhandled error: ${err.message}`, { stack: err.stack, path: req.path });
            const serviceName = req.baseUrl.split('/')[2] || 'unknown-service';
            const errorId = (0, uuid_1.v4)();
            const systemError = {
                id: errorId,
                serviceName: serviceName,
                code: err.code || 'UNKNOWN_ERROR',
                message: err.message,
                severity: monitoring_types_1.ErrorSeverity.CRITICAL,
                timestamp: new Date().toISOString(),
                details: {
                    stack: err.stack,
                    path: req.path,
                    method: req.method,
                    body: req.body,
                    query: req.query,
                    params: req.params,
                },
                isResolved: false,
            };
            try {
                await this.monitoringService['repository'].addSystemError(systemError);
                await this.monitoringService.triggerAlert({
                    id: (0, uuid_1.v4)(),
                    type: 'error',
                    serviceName: systemError.serviceName,
                    message: `System Error: ${systemError.message} (Code: ${systemError.code})`,
                    timestamp: systemError.timestamp,
                    severity: systemError.severity,
                    isAcknowledged: false,
                });
            }
            catch (monitoringError) {
                logger_1.default.error(`Failed to record or alert system error: ${monitoringError}`);
            }
            next(err);
        };
        this.healthCheck = (serviceName) => async (req, res, next) => {
            try {
                const healthStatus = {
                    serviceName: serviceName,
                    status: monitoring_types_1.HealthStatus.OK,
                    timestamp: new Date().toISOString(),
                    details: 'Service is operational.',
                };
                await this.monitoringService.recordSystemHealth(healthStatus);
                res.status(200).json(healthStatus);
            }
            catch (error) {
                logger_1.default.error(`Health check failed for ${serviceName}: ${error}`);
                res.status(500).json({ serviceName, status: monitoring_types_1.HealthStatus.CRITICAL, timestamp: new Date().toISOString(), details: 'Health check failed.' });
            }
        };
        const monitoringRepository = new monitoring_repository_1.MonitoringRepository();
        const auditTrailService = new audit_service_1.AuditTrailService();
        const paimService = new paim_service_1.PaimService(null, auditTrailService);
        const powerOpsService = new powerops_service_1.PowerOpsService();
        const culturalSensitivityService = new cultural_sensitivity_service_1.CulturalSensitivityService(db_1.default);
        this.monitoringService = new monitoring_service_1.MonitoringService(monitoringRepository, auditTrailService, paimService, powerOpsService, culturalSensitivityService);
    }
}
exports.MonitoringMiddleware = MonitoringMiddleware;
