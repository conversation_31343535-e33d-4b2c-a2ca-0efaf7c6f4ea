"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.resetMetrics = exports.getMetrics = exports.incrementMetric = void 0;
const logger_1 = __importDefault(require("../config/logger"));
const metrics = {
    api_requests_total: 0,
    api_errors_total: 0,
    db_queries_total: 0,
    db_query_errors_total: 0,
};
const incrementMetric = (metricName, value = 1) => {
    if (metrics.hasOwnProperty(metricName)) {
        metrics[metricName] += value;
    }
    else {
        logger_1.default.warn(`Attempted to increment unknown metric: ${metricName}`);
    }
};
exports.incrementMetric = incrementMetric;
const getMetrics = () => {
    return { ...metrics };
};
exports.getMetrics = getMetrics;
const resetMetrics = () => {
    for (const key in metrics) {
        if (metrics.hasOwnProperty(key)) {
            metrics[key] = 0;
        }
    }
    logger_1.default.info('Metrics reset.');
};
exports.resetMetrics = resetMetrics;
