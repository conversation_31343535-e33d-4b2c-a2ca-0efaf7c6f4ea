"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PowerOpsController = void 0;
const express_1 = require("express");
const powerops_service_1 = require("./powerops.service");
const asyncHandler_1 = require("../utils/asyncHandler");
const validation_1 = require("../utils/validation");
const errors_1 = require("../utils/errors");
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const type_guards_1 = require("../utils/type-guards");
const authorization_1 = require("../middleware/authorization");
const permissions_1 = require("../auth/permissions");
const powerops_types_1 = require("./powerops.types");
const powerops_validation_1 = require("./powerops.validation");
const xpLimiter = (0, express_rate_limit_1.default)({
    windowMs: 60 * 1000,
    max: 5,
    message: 'Too many XP requests from this IP, please try again after a minute.',
    keyGenerator: (req) => {
        const requestWithUser = req;
        return (requestWithUser.ip || 'unknown') + (requestWithUser.user ? `-${requestWithUser.user.userId}` : '');
    },
});
class PowerOpsController {
    constructor(notificationService) {
        this.logPowerOpsUsage = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const data = req.body;
            const usage = await this.powerOpsService.logUsage(req.user, data);
            res.status(201).json(usage);
        });
        this.getPowerOpsUsage = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const { entityId, entityType, startDate, endDate } = req.query;
            const usage = await this.powerOpsService.getUsage(req.user, (0, type_guards_1.requireParam)(entityId, 'entityId'), entityType, (0, type_guards_1.getParam)(startDate), (0, type_guards_1.getParam)(endDate));
            res.status(200).json(usage);
        });
        this.getXpByUser = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const { id } = req.params;
            const xp = await this.powerOpsService.getXp(req.user, (0, type_guards_1.requireParam)(id, 'id'), powerops_types_1.EntityType.User);
            res.status(200).json(xp);
        });
        this.getXpByOrg = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const { id } = req.params;
            const xp = await this.powerOpsService.getXp(req.user, (0, type_guards_1.requireParam)(id, 'id'), powerops_types_1.EntityType.PaimInstance);
            res.status(200).json(xp);
        });
        this.awardXp = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const data = req.body;
            const xp = await this.powerOpsService.awardXp(req.user, data);
            res.status(200).json(xp);
        });
        this.getAllBadges = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const badges = await this.powerOpsService.getAllBadges(req.user);
            res.status(200).json(badges);
        });
        this.getBadgesByUser = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const { id } = req.params;
            const badges = await this.powerOpsService.getBadges(req.user, (0, type_guards_1.requireParam)(id, 'id'), powerops_types_1.EntityType.User);
            res.status(200).json(badges);
        });
        this.awardBadge = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const data = req.body;
            const badge = await this.powerOpsService.awardBadge(req.user, data);
            res.status(200).json(badge);
        });
        this.getAchievements = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const { entityId, entityType } = req.query;
            const achievements = await this.powerOpsService.getAchievements(req.user, (0, type_guards_1.requireParam)(entityId, 'entityId'), (0, type_guards_1.requireParam)(entityType, 'entityType'));
            res.status(200).json(achievements);
        });
        this.grantAchievement = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const data = req.body;
            const achievement = await this.powerOpsService.grantAchievement(req.user, data);
            res.status(200).json(achievement);
        });
        this.getStreaks = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const { entityId, entityType } = req.query;
            const streaks = await this.powerOpsService.getStreaks(req.user, (0, type_guards_1.requireParam)(entityId, 'entityId'), (0, type_guards_1.requireParam)(entityType, 'entityType'));
            res.status(200).json(streaks);
        });
        this.getBudgets = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const { entityId, entityType } = req.query;
            const budgets = await this.powerOpsService.getBudgets(req.user, (0, type_guards_1.requireParam)(entityId, 'entityId'), (0, type_guards_1.requireParam)(entityType, 'entityType'));
            res.status(200).json(budgets);
        });
        this.createBudget = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const data = req.body;
            const budget = await this.powerOpsService.createBudget(req.user, data);
            res.status(201).json(budget);
        });
        this.updateBudget = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const { budgetId } = req.params;
            const data = req.body;
            const updatedBudget = await this.powerOpsService.updateBudget(req.user, (0, type_guards_1.requireParam)(budgetId, 'budgetId'), data);
            res.status(200).json(updatedBudget);
        });
        this.deleteBudget = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const { budgetId } = req.params;
            await this.powerOpsService.deleteBudget(req.user, (0, type_guards_1.requireParam)(budgetId, 'budgetId'));
            res.status(204).send();
        });
        this.getInvoices = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const { entityId, entityType } = req.query;
            const invoices = await this.powerOpsService.getInvoices(req.user, (0, type_guards_1.requireParam)(entityId, 'entityId'), (0, type_guards_1.requireParam)(entityType, 'entityType'));
            res.status(200).json(invoices);
        });
        this.createInvoice = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const data = req.body;
            const invoice = await this.powerOpsService.createInvoice(req.user, data);
            res.status(201).json(invoice);
        });
        this.processPayment = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const data = req.body;
            const payment = await this.powerOpsService.processPayment(req.user, data);
            res.status(200).json(payment);
        });
        this.getLeaderboard = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const { metric, limit } = req.query;
            const leaderboard = await this.powerOpsService.getLeaderboard(req.user, (0, type_guards_1.requireParam)(metric, 'metric'), limit ? parseInt((0, type_guards_1.getParam)(limit)) : undefined);
            res.status(200).json(leaderboard);
        });
        this.getCostOptimizationRecommendations = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const { entityId, entityType } = req.query;
            const recommendations = await this.powerOpsService.getCostOptimizationRecommendations(req.user, (0, type_guards_1.requireParam)(entityId, 'entityId'), (0, type_guards_1.requireParam)(entityType, 'entityType'));
            res.status(200).json(recommendations);
        });
        this.getResourceUsageLimits = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const { entityId, entityType } = req.query;
            const limits = await this.powerOpsService.getResourceUsageLimits(req.user, (0, type_guards_1.requireParam)(entityId, 'entityId'), (0, type_guards_1.requireParam)(entityType, 'entityType'));
            res.status(200).json(limits);
        });
        this.setResourceUsageLimit = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const data = req.body;
            const limit = await this.powerOpsService.setResourceUsageLimit(req.user, data);
            res.status(201).json(limit);
        });
        this.getNotifications = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const { entityId, entityType } = req.query;
            const notifications = await this.powerOpsService.getNotifications(req.user, (0, type_guards_1.requireParam)(entityId, 'entityId'), (0, type_guards_1.requireParam)(entityType, 'entityType'));
            res.status(200).json(notifications);
        });
        this.createNotification = (0, asyncHandler_1.asyncHandler)(async (req, res, next) => {
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const data = req.body;
            const notification = await this.powerOpsService.createNotification(req.user, data);
            res.status(201).json(notification);
        });
        this.powerOpsService = new powerops_service_1.PowerOpsService(undefined, undefined, notificationService);
        this.router = (0, express_1.Router)();
        this.initializeRoutes();
    }
    initializeRoutes() {
        this.router.post('/usage', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.POWER_OPS_CREATE]), (0, validation_1.validate)(powerops_validation_1.logPowerOpsUsageSchema), (0, asyncHandler_1.asyncHandler)(this.logPowerOpsUsage.bind(this)));
        this.router.get('/usage/:entityType/:entityId', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.POWER_OPS_READ]), (0, validation_1.validate)(powerops_validation_1.getPowerOpsUsageQuerySchema), (0, asyncHandler_1.asyncHandler)(this.getPowerOpsUsage.bind(this)));
        this.router.post('/xp/add', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.POWER_OPS_MANAGE_ALL]), xpLimiter, (0, validation_1.validate)(powerops_validation_1.awardXpSchema), (0, asyncHandler_1.asyncHandler)(this.awardXp.bind(this)));
        this.router.get('/xp/user/:id', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.POWER_OPS_READ]), (0, asyncHandler_1.asyncHandler)(this.getXpByUser.bind(this)));
        this.router.get('/xp/org/:id', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.POWER_OPS_READ]), (0, asyncHandler_1.asyncHandler)(this.getXpByOrg.bind(this)));
        this.router.post('/badges/award', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.POWER_OPS_MANAGE_ALL]), (0, validation_1.validate)(powerops_validation_1.awardBadgeSchema), (0, asyncHandler_1.asyncHandler)(this.awardBadge.bind(this)));
        this.router.get('/badges', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.POWER_OPS_READ]), (0, asyncHandler_1.asyncHandler)(this.getAllBadges.bind(this)));
        this.router.get('/badges/user/:id', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.POWER_OPS_READ]), (0, validation_1.validate)(powerops_validation_1.entityIdEntityTypeQuerySchema), (0, asyncHandler_1.asyncHandler)(this.getBadgesByUser.bind(this)));
        this.router.post('/achievements', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.POWER_OPS_MANAGE_ALL]), (0, validation_1.validate)(powerops_validation_1.grantAchievementSchema), (0, asyncHandler_1.asyncHandler)(this.grantAchievement.bind(this)));
        this.router.get('/achievements/:entityType/:entityId', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.POWER_OPS_READ]), (0, validation_1.validate)(powerops_validation_1.entityIdEntityTypeQuerySchema), (0, asyncHandler_1.asyncHandler)(this.getAchievements.bind(this)));
        this.router.get('/streaks/:entityType/:entityId', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.POWER_OPS_READ]), (0, validation_1.validate)(powerops_validation_1.entityIdEntityTypeQuerySchema), (0, asyncHandler_1.asyncHandler)(this.getStreaks.bind(this)));
        this.router.get('/leaderboard/:metric', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.POWER_OPS_READ]), (0, validation_1.validate)(powerops_validation_1.getLeaderboardQuerySchema), (0, asyncHandler_1.asyncHandler)(this.getLeaderboard.bind(this)));
        this.router.get('/budgets/:entityType/:entityId', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.BILLING_VIEW]), (0, validation_1.validate)(powerops_validation_1.entityIdEntityTypeQuerySchema), (0, asyncHandler_1.asyncHandler)(this.getBudgets.bind(this)));
        this.router.post('/budgets', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.BILLING_MANAGE]), (0, validation_1.validate)(powerops_validation_1.createBudgetSchema), (0, asyncHandler_1.asyncHandler)(this.createBudget.bind(this)));
        this.router.put('/budgets/:budgetId', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.BILLING_MANAGE]), (0, validation_1.validate)(powerops_validation_1.updateBudgetSchema), (0, asyncHandler_1.asyncHandler)(this.updateBudget.bind(this)));
        this.router.delete('/budgets/:budgetId', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.BILLING_MANAGE]), (0, asyncHandler_1.asyncHandler)(this.deleteBudget.bind(this)));
        this.router.get('/invoices/:entityType/:entityId', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.BILLING_VIEW]), (0, validation_1.validate)(powerops_validation_1.entityIdEntityTypeQuerySchema), (0, asyncHandler_1.asyncHandler)(this.getInvoices.bind(this)));
        this.router.post('/invoices', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.BILLING_MANAGE]), (0, validation_1.validate)(powerops_validation_1.createInvoiceSchema), (0, asyncHandler_1.asyncHandler)(this.createInvoice.bind(this)));
        this.router.post('/payments', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.BILLING_MANAGE]), (0, validation_1.validate)(powerops_validation_1.processPaymentSchema), (0, asyncHandler_1.asyncHandler)(this.processPayment.bind(this)));
        this.router.get('/recommendations/:entityType/:entityId', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.BILLING_VIEW]), (0, validation_1.validate)(powerops_validation_1.entityIdEntityTypeQuerySchema), (0, asyncHandler_1.asyncHandler)(this.getCostOptimizationRecommendations.bind(this)));
        this.router.get('/resource-limits/:entityType/:entityId', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.BILLING_VIEW]), (0, validation_1.validate)(powerops_validation_1.entityIdEntityTypeQuerySchema), (0, asyncHandler_1.asyncHandler)(this.getResourceUsageLimits.bind(this)));
        this.router.post('/resource-limits', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.BILLING_MANAGE]), (0, validation_1.validate)(powerops_validation_1.setResourceUsageLimitSchema), (0, asyncHandler_1.asyncHandler)(this.setResourceUsageLimit.bind(this)));
        this.router.get('/notifications/:entityType/:entityId', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.POWER_OPS_READ]), (0, validation_1.validate)(powerops_validation_1.entityIdEntityTypeQuerySchema), (0, asyncHandler_1.asyncHandler)(this.getNotifications.bind(this)));
        this.router.post('/notifications', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.POWER_OPS_CREATE]), (0, validation_1.validate)(powerops_validation_1.createNotificationSchema), (0, asyncHandler_1.asyncHandler)(this.createNotification.bind(this)));
    }
}
exports.PowerOpsController = PowerOpsController;
