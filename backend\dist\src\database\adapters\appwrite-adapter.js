"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppwriteAdapter = void 0;
const node_appwrite_1 = require("node-appwrite");
const appwrite_1 = require("../../config/appwrite");
class AppwriteAdapter {
    constructor() {
        this.client = appwrite_1.appwriteService.getClient();
        this.account = appwrite_1.appwriteService.getAccount();
        this.databases = appwrite_1.appwriteService.getDatabases();
        this.storage = appwrite_1.appwriteService.getStorage();
        this.users = appwrite_1.appwriteService.getUsers();
    }
    async signUp(email, password, metadata) {
        const user = await this.users.create('unique()', email, password, undefined, JSON.stringify(metadata));
        return user;
    }
    async signIn(email, password) {
        const session = await this.account.createEmailPasswordSession(email, password);
        return session;
    }
    async signOut() {
        await this.account.deleteSession('current');
    }
    async getCurrentUser() {
        try {
            const user = await this.account.get();
            return user;
        }
        catch (error) {
            if (error instanceof node_appwrite_1.AppwriteException && error.code === 401) {
                return null;
            }
            throw error;
        }
    }
    async updateUser(userId, data) {
        let updatedUser;
        if ('name' in data && typeof data.name === 'string') {
            updatedUser = await this.users.updateName(userId, data.name);
        }
        if ('email' in data && typeof data.email === 'string') {
            updatedUser = await this.users.updateEmail(userId, data.email);
        }
        if ('password' in data && typeof data.password === 'string') {
            updatedUser = await this.users.updatePassword(userId, data.password);
        }
        return updatedUser;
    }
    async createDocument(collectionId, data, documentId) {
        const doc = await this.databases.createDocument(process.env.APPWRITE_DATABASE_ID, collectionId, documentId || 'unique()', data);
        return doc;
    }
    async getDocument(collectionId, documentId) {
        const doc = await this.databases.getDocument(process.env.APPWRITE_DATABASE_ID, collectionId, documentId);
        return doc;
    }
    async updateDocument(collectionId, documentId, data) {
        const updatedDoc = await this.databases.updateDocument(process.env.APPWRITE_DATABASE_ID, collectionId, documentId, data);
        return updatedDoc;
    }
    async deleteDocument(collectionId, documentId) {
        await this.databases.deleteDocument(process.env.APPWRITE_DATABASE_ID, collectionId, documentId);
    }
    async listDocuments(collectionId, queries) {
        const response = await this.databases.listDocuments(process.env.APPWRITE_DATABASE_ID, collectionId, queries);
        return response.documents;
    }
    async uploadFile(bucketId, file, fileName) {
        const uploadedFile = await this.storage.createFile(bucketId, 'unique()', file);
        return uploadedFile;
    }
    async getFile(bucketId, fileId) {
        const fileBuffer = await this.storage.getFileDownload(bucketId, fileId);
        return new Blob([fileBuffer]);
    }
    async deleteFile(bucketId, fileId) {
        await this.storage.deleteFile(bucketId, fileId);
    }
    async listFiles(bucketId) {
        const response = await this.storage.listFiles(bucketId);
        return response.files;
    }
    async getUserByEmail(email) {
        const response = await this.databases.listDocuments(process.env.APPWRITE_DATABASE_ID, 'users', [`email=${email}`]);
        return response.documents.length > 0 ? response.documents[0] : null;
    }
    async createUser(userData) {
        const { email, password, ...metadata } = userData;
        return this.signUp(email, password, metadata);
    }
    async createSession(userId, data) {
        console.warn('createSession in AppwriteAdapter might need custom implementation based on AuthService usage.');
        return { session_id: 'mock_session_id_appwrite', userId, ...data };
    }
    async getUserById(userId) {
        try {
            const user = await this.users.get(userId);
            return user;
        }
        catch (error) {
            if (error instanceof node_appwrite_1.AppwriteException && error.code === 404) {
                return null;
            }
            throw error;
        }
    }
    async deleteSession(sessionId) {
        await this.account.deleteSession(sessionId);
    }
    async healthCheck() {
        try {
            await appwrite_1.appwriteService.healthCheck();
            return true;
        }
        catch (error) {
            console.error('Appwrite health check failed:', error);
            return false;
        }
    }
}
exports.AppwriteAdapter = AppwriteAdapter;
