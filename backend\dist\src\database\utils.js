"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.withTransaction = withTransaction;
exports.generateUuid = generateUuid;
const logger_1 = __importDefault(require("../config/logger"));
async function withTransaction(knex, callback) {
    return knex.transaction(async (trx) => {
        try {
            const result = await callback(trx);
            await trx.commit();
            return result;
        }
        catch (error) {
            await trx.rollback();
            logger_1.default.error('Transaction failed, rolling back:', error.message, error.stack);
            throw error;
        }
    });
}
function generateUuid() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}
