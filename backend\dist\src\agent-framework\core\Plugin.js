"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PluginRegistry = exports.Plugin = void 0;
const Config_1 = require("./Config");
const logger_1 = __importDefault(require("../../config/logger"));
class Plugin {
    constructor(metadata, config) {
        this.hooks = {};
        this.isInstalled = false;
        this.installedAgents = new Set();
        this.metadata = metadata;
        this.config = config || new Config_1.Config();
        logger_1.default.debug(`Plugin ${this.metadata.name} v${this.metadata.version} created`);
    }
    get name() {
        return this.metadata.name;
    }
    get version() {
        return this.metadata.version;
    }
    get installed() {
        return this.isInstalled;
    }
    getInstalledAgents() {
        return Array.from(this.installedAgents);
    }
    async install(agent) {
        if (this.installedAgents.has(agent.id)) {
            throw new Error(`Plugin ${this.name} is already installed on agent ${agent.name}`);
        }
        try {
            if (this.hooks.beforeInstall) {
                await this.hooks.beforeInstall(agent);
            }
            await this.checkDependencies(agent);
            await this.onInstall(agent);
            this.registerEventListeners(agent);
            this.installedAgents.add(agent.id);
            this.isInstalled = true;
            if (this.hooks.afterInstall) {
                await this.hooks.afterInstall(agent);
            }
            logger_1.default.info(`Plugin ${this.name} installed on agent ${agent.name}`);
        }
        catch (error) {
            logger_1.default.error(`Failed to install plugin ${this.name} on agent ${agent.name}:`, error);
            throw error;
        }
    }
    async uninstall(agent) {
        if (!this.installedAgents.has(agent.id)) {
            throw new Error(`Plugin ${this.name} is not installed on agent ${agent.name}`);
        }
        try {
            if (this.hooks.beforeUninstall) {
                await this.hooks.beforeUninstall(agent);
            }
            this.unregisterEventListeners(agent);
            await this.onUninstall(agent);
            this.installedAgents.delete(agent.id);
            if (this.installedAgents.size === 0) {
                this.isInstalled = false;
            }
            if (this.hooks.afterUninstall) {
                await this.hooks.afterUninstall(agent);
            }
            logger_1.default.info(`Plugin ${this.name} uninstalled from agent ${agent.name}`);
        }
        catch (error) {
            logger_1.default.error(`Failed to uninstall plugin ${this.name} from agent ${agent.name}:`, error);
            throw error;
        }
    }
    configure(config) {
        if (config instanceof Config_1.Config) {
            this.config = config;
        }
        else {
            this.config.load(config);
        }
        logger_1.default.debug(`Plugin ${this.name} configured`);
        return this;
    }
    getConfig() {
        return this.config;
    }
    setHooks(hooks) {
        this.hooks = { ...this.hooks, ...hooks };
        logger_1.default.debug(`Plugin ${this.name} hooks updated`);
        return this;
    }
    async isCompatible(agent) {
        try {
            await this.checkCompatibility(agent);
            return true;
        }
        catch {
            return false;
        }
    }
    getStatus() {
        return {
            installed: this.isInstalled,
            agentCount: this.installedAgents.size,
            metadata: { ...this.metadata }
        };
    }
    async checkCompatibility(agent) {
        logger_1.default.debug(`Checking compatibility for plugin ${this.name} with agent ${agent.name}`);
    }
    async checkDependencies(agent) {
        if (!this.metadata.dependencies || this.metadata.dependencies.length === 0) {
            return;
        }
        const agentCapabilities = agent.getCapabilities().map(c => c.name);
        for (const dependency of this.metadata.dependencies) {
            if (!agentCapabilities.includes(dependency)) {
                throw new Error(`Plugin ${this.name} requires capability '${dependency}' which is not available on agent ${agent.name}`);
            }
        }
        logger_1.default.debug(`All dependencies satisfied for plugin ${this.name}`);
    }
    registerEventListeners(agent) {
        if (this.hooks.onAgentStart) {
            agent.on('agent:start', this.hooks.onAgentStart);
        }
        if (this.hooks.onAgentStop) {
            agent.on('agent:stop', this.hooks.onAgentStop);
        }
        if (this.hooks.onTaskExecute) {
            agent.on('task:start', this.hooks.onTaskExecute);
        }
        logger_1.default.debug(`Event listeners registered for plugin ${this.name}`);
    }
    unregisterEventListeners(agent) {
        if (this.hooks.onAgentStart) {
            agent.off('agent:start', this.hooks.onAgentStart);
        }
        if (this.hooks.onAgentStop) {
            agent.off('agent:stop', this.hooks.onAgentStop);
        }
        if (this.hooks.onTaskExecute) {
            agent.off('task:start', this.hooks.onTaskExecute);
        }
        logger_1.default.debug(`Event listeners unregistered for plugin ${this.name}`);
    }
}
exports.Plugin = Plugin;
class PluginRegistry {
    constructor() {
        this.plugins = new Map();
    }
    static getInstance() {
        if (!PluginRegistry.instance) {
            PluginRegistry.instance = new PluginRegistry();
        }
        return PluginRegistry.instance;
    }
    register(plugin) {
        if (this.plugins.has(plugin.name)) {
            throw new Error(`Plugin ${plugin.name} is already registered`);
        }
        this.plugins.set(plugin.name, plugin);
        logger_1.default.info(`Plugin ${plugin.name} registered in registry`);
    }
    unregister(pluginName) {
        const removed = this.plugins.delete(pluginName);
        if (removed) {
            logger_1.default.info(`Plugin ${pluginName} unregistered from registry`);
        }
        return removed;
    }
    get(pluginName) {
        return this.plugins.get(pluginName);
    }
    getAll() {
        return Array.from(this.plugins.values());
    }
    getByCapability(capability) {
        return Array.from(this.plugins.values()).filter(plugin => plugin.metadata.capabilities?.includes(capability));
    }
    clear() {
        this.plugins.clear();
        logger_1.default.info('Plugin registry cleared');
    }
}
exports.PluginRegistry = PluginRegistry;
