"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowCollaborationMiddleware = void 0;
const errors_1 = require("../utils/errors");
class WorkflowCollaborationMiddleware {
    constructor() {
        this.validateCreateWorkflow = (req, res, next) => {
            const { name, ownerId, paimInstanceId, definition } = req.body;
            if (!name || !ownerId || !paimInstanceId || !definition) {
                throw new errors_1.CustomError('Missing required fields for workflow creation', { originalErrorCode: 'ValidationError', originalStatusCode: 400 });
            }
            next();
        };
        this.checkWorkflowPermission = (requiredPermission) => {
            return (req, res, next) => {
                const hasPermission = true;
                if (!hasPermission) {
                    throw new errors_1.CustomError('User does not have sufficient permissions for this workflow', { originalErrorCode: 'AuthorizationError', originalStatusCode: 403 });
                }
                next();
            };
        };
        this.validateCreateTask = (req, res, next) => {
            const { title, status, priority } = req.body;
            if (!title || !status || !priority) {
                throw new errors_1.CustomError('Missing required fields for task creation', { originalErrorCode: 'ValidationError', originalStatusCode: 400 });
            }
            next();
        };
        this.validateStartCollaborationSession = (req, res, next) => {
            const { name, ownerId, paimInstanceId } = req.body;
            if (!name || !ownerId || !paimInstanceId) {
                throw new errors_1.CustomError('Missing required fields for collaboration session creation', { originalErrorCode: 'ValidationError', originalStatusCode: 400 });
            }
            next();
        };
        this.validateCrossTenantMessage = (req, res, next) => {
            const { senderPaimInstanceId, recipientPaimInstanceId, messageContent } = req.body;
            if (!senderPaimInstanceId || !recipientPaimInstanceId || !messageContent) {
                throw new errors_1.CustomError('Missing required fields for cross-tenant message', { originalErrorCode: 'ValidationError', originalStatusCode: 400 });
            }
            next();
        };
        this.validateShareWorkflow = (req, res, next) => {
            const { workflowId, sharedWithId, sharedWithEntityType, permissionLevel } = req.body;
            if (!workflowId || !sharedWithId || !sharedWithEntityType || !permissionLevel) {
                throw new errors_1.CustomError('Missing required fields for workflow sharing', { originalErrorCode: 'ValidationError', originalStatusCode: 400 });
            }
            next();
        };
        this.validateDelegateTask = (req, res, next) => {
            const { delegateToId, delegateToEntityType } = req.body;
            if (!delegateToId || !delegateToEntityType) {
                throw new errors_1.CustomError('Missing required fields for task delegation', { originalErrorCode: 'ValidationError', originalStatusCode: 400 });
            }
            next();
        };
        this.validateCreateWorkspace = (req, res, next) => {
            const { name, ownerId, paimInstanceId } = req.body;
            if (!name || !ownerId || !paimInstanceId) {
                throw new errors_1.CustomError('Missing required fields for workspace creation', { originalErrorCode: 'ValidationError', originalStatusCode: 400 });
            }
            next();
        };
        this.validateCreateTeam = (req, res, next) => {
            const { name, paimInstanceId } = req.body;
            if (!name || !paimInstanceId) {
                throw new errors_1.CustomError('Missing required fields for team creation', { originalErrorCode: 'ValidationError', originalStatusCode: 400 });
            }
            next();
        };
    }
}
exports.WorkflowCollaborationMiddleware = WorkflowCollaborationMiddleware;
