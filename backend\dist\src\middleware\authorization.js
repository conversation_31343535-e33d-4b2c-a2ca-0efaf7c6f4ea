"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.hasRole = exports.authorize = void 0;
const authorization_service_1 = require("../auth/authorization.service");
const errors_1 = require("../utils/errors");
const authorize = (requiredPermissions) => {
    return (req, res, next) => {
        const user = req.user;
        if (!user) {
            return next(new errors_1.AuthorizationError('Unauthorized: No user authenticated', 401));
        }
        const { roles, paimTier } = user;
        if (!authorization_service_1.authorizationService.hasAnyPermission(roles, paimTier, requiredPermissions)) {
            return next(new errors_1.AuthorizationError('Forbidden: Insufficient permissions', 403));
        }
        next();
    };
};
exports.authorize = authorize;
const hasRole = (requiredRole) => {
    return (req, res, next) => {
        const user = req.user;
        if (!user) {
            return next(new errors_1.AuthorizationError('Unauthorized: No user authenticated', 401));
        }
        if (!authorization_service_1.authorizationService.hasRole(user.roles, requiredRole)) {
            return next(new errors_1.AuthorizationError(`Forbidden: Requires ${requiredRole} role`, 403));
        }
        next();
    };
};
exports.hasRole = hasRole;
