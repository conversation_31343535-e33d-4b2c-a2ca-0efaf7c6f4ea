"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaimController = void 0;
const express_1 = require("express");
const paim_service_1 = require("./paim.service");
const paim_repository_1 = require("./paim.repository");
const audit_service_1 = require("../audit/audit.service");
const asyncHandler_1 = require("../utils/asyncHandler");
const validation_1 = require("../utils/validation");
const errors_1 = require("../utils/errors");
const type_guards_1 = require("../utils/type-guards");
const paim_validation_1 = require("./paim.validation");
const authorization_1 = require("../middleware/authorization");
const permissions_1 = require("../auth/permissions");
class PaimController {
    constructor(notificationService, wsService) {
        this.auditTrailService = new audit_service_1.AuditTrailService();
        this.paimRepository = new paim_repository_1.PaimRepository(this.auditTrailService);
        this.paimService = new paim_service_1.PaimService(this.paimRepository, this.auditTrailService, notificationService, wsService);
        this.router = (0, express_1.Router)();
        this.initializeRoutes();
    }
    initializeRoutes() {
        this.router.get('/', (0, validation_1.validate)(paim_validation_1.getAllPaimInstancesQuerySchema), (0, authorization_1.authorize)([permissions_1.PERMISSIONS.PAIM_READ]), (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { status, page, size, sort } = req.query;
            const filters = { status: (0, type_guards_1.getParam)(status) };
            const pagination = { page: parseInt((0, type_guards_1.getParam)(page, '1')), size: parseInt((0, type_guards_1.getParam)(size, '10')), sort: (0, type_guards_1.getParam)(sort, 'createdAt,desc') };
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const { data, totalElements } = await this.paimService.getAllPaimInstances(req.user, filters, pagination);
            const paginationMetadata = {
                totalElements,
                totalPages: Math.ceil(totalElements / pagination.size),
                currentPage: pagination.page,
                pageSize: pagination.size,
            };
            res.status(200).json({ data, pagination: paginationMetadata });
        }));
        this.router.post('/', (0, validation_1.validate)(paim_validation_1.createPaimInstanceSchema), (0, authorization_1.authorize)([permissions_1.PERMISSIONS.PAIM_CREATE]), (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const paimInstance = await this.paimService.createPaimInstance(req.body, req.user);
            res.status(201).json(paimInstance);
        }));
        this.router.get('/:paimInstanceId', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.PAIM_READ]), (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { paimInstanceId } = req.params;
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const paimInstance = await this.paimService.getPaimInstanceById((0, type_guards_1.requireParam)(paimInstanceId, 'paimInstanceId'), req.user);
            res.status(200).json(paimInstance);
        }));
        this.router.put('/:paimInstanceId', (0, validation_1.validate)(paim_validation_1.updatePaimInstanceSchema), (0, authorization_1.authorize)([permissions_1.PERMISSIONS.PAIM_UPDATE]), (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { paimInstanceId } = req.params;
            const updateRequest = req.body;
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const updatedPaimInstance = await this.paimService.updatePaimInstance((0, type_guards_1.requireParam)(paimInstanceId, 'paimInstanceId'), req.user, updateRequest);
            res.status(200).json(updatedPaimInstance);
        }));
        this.router.delete('/:paimInstanceId', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.PAIM_DELETE]), (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { paimInstanceId } = req.params;
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            await this.paimService.deletePaimInstance((0, type_guards_1.requireParam)(paimInstanceId, 'paimInstanceId'), req.user);
            res.status(204).send();
        }));
        this.router.post('/:paimInstanceId/tier-change-requests', (0, validation_1.validate)(paim_validation_1.paimTierChangeRequestSchema), (0, authorization_1.authorize)([permissions_1.PERMISSIONS.PAIM_MANAGE_TIERS]), (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { paimInstanceId } = req.params;
            const tierChangeRequest = req.body;
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const status = await this.paimService.requestPaimTierChange((0, type_guards_1.requireParam)(paimInstanceId, 'paimInstanceId'), req.user, tierChangeRequest);
            res.status(202).json(status);
        }));
        this.router.get('/:paimInstanceId/hierarchy', (0, authorization_1.authorize)([permissions_1.PERMISSIONS.PAIM_READ]), (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { paimInstanceId } = req.params;
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const hierarchy = await this.paimService.getPaimHierarchy((0, type_guards_1.requireParam)(paimInstanceId, 'paimInstanceId'), req.user);
            res.status(200).json(hierarchy);
        }));
        this.router.put('/:paimInstanceId/hierarchy', (0, validation_1.validate)(paim_validation_1.paimHierarchyUpdateSchema), (0, authorization_1.authorize)([permissions_1.PERMISSIONS.PAIM_MANAGE_TIERS]), (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { paimInstanceId } = req.params;
            const hierarchyUpdate = req.body;
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const updatedHierarchy = await this.paimService.updatePaimHierarchy((0, type_guards_1.requireParam)(paimInstanceId, 'paimInstanceId'), req.user, hierarchyUpdate);
            res.status(200).json(updatedHierarchy);
        }));
        this.router.post('/:paimInstanceId/communicate', (0, validation_1.validate)(paim_validation_1.crossPaimCommunicationRequestSchema), (0, authorization_1.authorize)([permissions_1.PERMISSIONS.PAIM_VIEW_ALL]), (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { paimInstanceId } = req.params;
            const communicationRequest = req.body;
            if (!req.user) {
                throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
            }
            const response = await this.paimService.communicateWithPaim((0, type_guards_1.requireParam)(paimInstanceId, 'paimInstanceId'), req.user, communicationRequest.targetPaimInstanceId, communicationRequest.message, communicationRequest.messageType || 'text');
            res.status(200).json(response);
        }));
    }
}
exports.PaimController = PaimController;
