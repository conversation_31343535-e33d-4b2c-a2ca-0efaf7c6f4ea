"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowEngine = exports.ToolRegistry = exports.DevOpsAgent = exports.R1LogicAgent = exports.CLAPIAgent = exports.PluginRegistry = exports.Plugin = exports.EventEmitter = exports.Config = exports.Agent = exports.AgentFramework = void 0;
const Agent_1 = require("./core/Agent");
Object.defineProperty(exports, "Agent", { enumerable: true, get: function () { return Agent_1.Agent; } });
const Config_1 = require("./core/Config");
Object.defineProperty(exports, "Config", { enumerable: true, get: function () { return Config_1.Config; } });
const EventEmitter_1 = require("./core/EventEmitter");
Object.defineProperty(exports, "EventEmitter", { enumerable: true, get: function () { return EventEmitter_1.EventEmitter; } });
const Plugin_1 = require("./core/Plugin");
Object.defineProperty(exports, "Plugin", { enumerable: true, get: function () { return Plugin_1.Plugin; } });
Object.defineProperty(exports, "PluginRegistry", { enumerable: true, get: function () { return Plugin_1.PluginRegistry; } });
const CLAPIAgent_1 = require("./agents/CLAPIAgent");
Object.defineProperty(exports, "CLAPIAgent", { enumerable: true, get: function () { return CLAPIAgent_1.CLAPIAgent; } });
const R1LogicAgent_1 = require("./agents/R1LogicAgent");
Object.defineProperty(exports, "R1LogicAgent", { enumerable: true, get: function () { return R1LogicAgent_1.R1LogicAgent; } });
const DevOpsAgent_1 = require("./agents/DevOpsAgent");
Object.defineProperty(exports, "DevOpsAgent", { enumerable: true, get: function () { return DevOpsAgent_1.DevOpsAgent; } });
const ToolRegistry_1 = require("./tools/ToolRegistry");
Object.defineProperty(exports, "ToolRegistry", { enumerable: true, get: function () { return ToolRegistry_1.ToolRegistry; } });
const WorkflowEngine_1 = require("./workflow/WorkflowEngine");
Object.defineProperty(exports, "WorkflowEngine", { enumerable: true, get: function () { return WorkflowEngine_1.WorkflowEngine; } });
const logger_1 = __importDefault(require("../config/logger"));
const joi_1 = __importDefault(require("joi"));
const DEFAULT_CONFIG = {
    core: {
        logLevel: 'info',
        timeout: 30000,
        maxConcurrency: 5
    },
    agentRegistry: {
        'CL-API': {
            enabled: true,
            config: {
                rateLimit: {
                    enabled: true,
                    requestsPerMinute: 100,
                    burstLimit: 10
                }
            }
        },
        'R1-Logic': {
            enabled: true,
            config: {
                cache: {
                    timeout: 300000
                }
            }
        },
        'K1-Perf': {
            enabled: false
        },
        'UX-Design': {
            enabled: false
        },
        'DevOps': {
            enabled: true,
            config: {
                alerts: {
                    cpu: { threshold: 80 },
                    memory: { threshold: 85 },
                    disk: { threshold: 90 },
                    responseTime: { threshold: 5000 }
                }
            }
        }
    }
};
const CONFIG_SCHEMA = joi_1.default.object({
    core: joi_1.default.object({
        logLevel: joi_1.default.string().valid('debug', 'info', 'warn', 'error').default('info'),
        timeout: joi_1.default.number().min(1000).max(300000).default(30000),
        maxConcurrency: joi_1.default.number().min(1).max(100).default(5)
    }).required(),
    agentRegistry: joi_1.default.object().pattern(joi_1.default.string(), joi_1.default.object({
        enabled: joi_1.default.boolean().required(),
        config: joi_1.default.object().optional()
    })).required(),
    agents: joi_1.default.object().pattern(joi_1.default.string(), joi_1.default.object({
        enabled: joi_1.default.boolean().required(),
        module: joi_1.default.string().optional(),
        config: joi_1.default.object().optional()
    })).optional(),
    plugins: joi_1.default.object().pattern(joi_1.default.string(), joi_1.default.object({
        enabled: joi_1.default.boolean().required(),
        config: joi_1.default.object().optional()
    })).optional()
});
class AgentFramework extends EventEmitter_1.EventEmitter {
    constructor() {
        super();
        this.initialized = false;
        this.predefinedAgents = new Map();
        this.config = new Config_1.Config();
        this.toolRegistry = ToolRegistry_1.ToolRegistry.getInstance();
        this.workflowEngine = WorkflowEngine_1.WorkflowEngine.getInstance();
        this.pluginRegistry = Plugin_1.PluginRegistry.getInstance();
    }
    static getInstance() {
        if (!AgentFramework.instance) {
            AgentFramework.instance = new AgentFramework();
        }
        return AgentFramework.instance;
    }
    async initialize(userConfig) {
        if (this.initialized) {
            logger_1.default.warn('Framework is already initialized');
            return;
        }
        try {
            const mergedConfig = this.mergeConfig(DEFAULT_CONFIG, userConfig || {});
            const { error, value } = CONFIG_SCHEMA.validate(mergedConfig);
            if (error) {
                throw new Error(`Configuration validation failed: ${error.message}`);
            }
            this.config.load(value);
            await this.initializeCore();
            await this.initializePredefinedAgents();
            if (value.agents) {
                await this.initializeCustomAgents(value.agents);
            }
            if (value.plugins) {
                await this.initializePlugins(value.plugins);
            }
            this.initialized = true;
            this.emit('framework:initialized', { config: value });
            logger_1.default.info('AI Agent Framework initialized successfully');
        }
        catch (error) {
            logger_1.default.error('Framework initialization failed:', error);
            throw error;
        }
    }
    async shutdown() {
        if (!this.initialized) {
            return;
        }
        try {
            for (const agent of this.predefinedAgents.values()) {
                await agent.stop();
            }
            this.toolRegistry.clear();
            this.pluginRegistry.clear();
            this.initialized = false;
            this.emit('framework:shutdown');
            logger_1.default.info('AI Agent Framework shutdown completed');
        }
        catch (error) {
            logger_1.default.error('Framework shutdown failed:', error);
            throw error;
        }
    }
    getConfig() {
        return this.config;
    }
    getAgent(name) {
        return this.predefinedAgents.get(name) || Agent_1.Agent.getAgent(name);
    }
    getPredefinedAgents() {
        return new Map(this.predefinedAgents);
    }
    getToolRegistry() {
        return this.toolRegistry;
    }
    getWorkflowEngine() {
        return this.workflowEngine;
    }
    getPluginRegistry() {
        return this.pluginRegistry;
    }
    isInitialized() {
        return this.initialized;
    }
    static validate(config) {
        const { error } = CONFIG_SCHEMA.validate(config);
        if (error) {
            throw new Error(`Configuration validation failed: ${error.message}`);
        }
    }
    createAgent(id, name, type, config) {
        const agent = new Agent_1.Agent(id, name, type, config);
        Agent_1.Agent.registerAgent(name, agent);
        return agent;
    }
    async executeTask(taskType, payload, agentName) {
        if (!this.initialized) {
            throw new Error('Framework is not initialized');
        }
        let agent;
        if (agentName) {
            agent = this.getAgent(agentName);
            if (!agent) {
                throw new Error(`Agent '${agentName}' not found`);
            }
        }
        else {
            agent = this.findBestAgent(taskType);
            if (!agent) {
                throw new Error(`No suitable agent found for task type '${taskType}'`);
            }
        }
        const task = {
            id: `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
            type: taskType,
            payload,
            timestamp: new Date()
        };
        return await agent.execute(task);
    }
    getStatus() {
        const agents = Array.from(this.predefinedAgents.values()).map(agent => ({
            name: agent.name,
            active: agent.getIsActive(),
            type: agent.type
        }));
        return {
            initialized: this.initialized,
            agents,
            tools: this.toolRegistry.getAll().length,
            workflows: this.workflowEngine.getAllWorkflows().length,
            plugins: this.pluginRegistry.getAll().length
        };
    }
    mergeConfig(defaultConfig, userConfig) {
        return {
            core: { ...defaultConfig.core, ...userConfig.core },
            agentRegistry: { ...defaultConfig.agentRegistry, ...userConfig.agentRegistry },
            agents: userConfig.agents || {},
            plugins: userConfig.plugins || {}
        };
    }
    async initializeCore() {
        const coreConfig = this.config.get('core');
        if (coreConfig.logLevel) {
        }
        logger_1.default.info('Core framework components initialized');
    }
    async initializePredefinedAgents() {
        const agentRegistry = this.config.get('agentRegistry');
        for (const [agentName, agentConfig] of Object.entries(agentRegistry)) {
            if (!agentConfig.enabled) {
                continue;
            }
            try {
                let agent;
                const config = new Config_1.Config(agentConfig.config || {});
                switch (agentName) {
                    case 'CL-API':
                        agent = new CLAPIAgent_1.CLAPIAgent(config);
                        break;
                    case 'R1-Logic':
                        agent = new R1LogicAgent_1.R1LogicAgent(config);
                        break;
                    case 'DevOps':
                        agent = new DevOpsAgent_1.DevOpsAgent(config);
                        break;
                    default:
                        logger_1.default.warn(`Unknown predefined agent: ${agentName}`);
                        continue;
                }
                await agent.start();
                this.predefinedAgents.set(agentName, agent);
                Agent_1.Agent.registerAgent(agentName, agent);
                logger_1.default.info(`Predefined agent '${agentName}' initialized and started`);
            }
            catch (error) {
                logger_1.default.error(`Failed to initialize predefined agent '${agentName}':`, error);
            }
        }
    }
    async initializeCustomAgents(agentsConfig) {
        for (const [agentName, agentConfig] of Object.entries(agentsConfig)) {
            if (!agentConfig.enabled) {
                continue;
            }
            try {
                logger_1.default.info(`Custom agent '${agentName}' would be initialized here`);
            }
            catch (error) {
                logger_1.default.error(`Failed to initialize custom agent '${agentName}':`, error);
            }
        }
    }
    async initializePlugins(pluginsConfig) {
        for (const [pluginName, pluginConfig] of Object.entries(pluginsConfig)) {
            if (!pluginConfig.enabled) {
                continue;
            }
            try {
                logger_1.default.info(`Plugin '${pluginName}' would be initialized here`);
            }
            catch (error) {
                logger_1.default.error(`Failed to initialize plugin '${pluginName}':`, error);
            }
        }
    }
    findBestAgent(taskType) {
        if (taskType.includes('api') || taskType.includes('request')) {
            return this.predefinedAgents.get('CL-API');
        }
        if (taskType.includes('logic') || taskType.includes('business') || taskType.includes('decision')) {
            return this.predefinedAgents.get('R1-Logic');
        }
        if (taskType.includes('deploy') || taskType.includes('health') || taskType.includes('monitor')) {
            return this.predefinedAgents.get('DevOps');
        }
        return Array.from(this.predefinedAgents.values())[0];
    }
}
exports.AgentFramework = AgentFramework;
__exportStar(require("./core/Agent"), exports);
__exportStar(require("./core/Config"), exports);
__exportStar(require("./core/Plugin"), exports);
__exportStar(require("./agents/CLAPIAgent"), exports);
__exportStar(require("./agents/R1LogicAgent"), exports);
__exportStar(require("./agents/DevOpsAgent"), exports);
__exportStar(require("./tools/ToolRegistry"), exports);
__exportStar(require("./workflow/WorkflowEngine"), exports);
exports.default = AgentFramework;
