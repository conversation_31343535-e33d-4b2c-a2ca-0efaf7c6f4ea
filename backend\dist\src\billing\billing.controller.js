"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BillingController = void 0;
const express_1 = require("express");
const asyncHandler_1 = require("../utils/asyncHandler");
const validation_1 = require("../utils/validation");
const joi_1 = __importDefault(require("joi"));
const authentication_1 = require("../middleware/authentication");
const authorization_1 = require("../middleware/authorization");
const permissions_1 = require("../auth/permissions");
const errors_1 = require("../utils/errors");
const PowerOpsUsageSchema = joi_1.default.object({
    org_id: joi_1.default.string().guid().required(),
    powerops_used: joi_1.default.number().positive().required(),
});
class BillingController {
    constructor(billingService) {
        this.billingService = billingService;
        this.router = (0, express_1.Router)();
        this.initializeRoutes();
    }
    initializeRoutes() {
        this.router.post('/usage', authentication_1.authenticate, (0, authorization_1.authorize)([permissions_1.PERMISSIONS.BILLING_MANAGE]), (0, validation_1.validate)(PowerOpsUsageSchema), (0, asyncHandler_1.asyncHandler)(this.trackUsageWebhook.bind(this)));
    }
    async trackUsageWebhook(req, res) {
        if (!req.user) {
            throw new errors_1.CustomError('User not authenticated', { originalStatusCode: 401 });
        }
        const { org_id, powerops_used } = req.body;
        const record = await this.billingService.trackPowerOpsUsage(req.user, org_id, powerops_used);
        res.status(200).json({ message: 'PowerOps usage tracked successfully', record });
    }
}
exports.BillingController = BillingController;
