"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.WorkflowCollaborationController = void 0;
const express = __importStar(require("express"));
const asyncHandler_1 = require("../utils/asyncHandler");
const validation_1 = require("../utils/validation");
const type_guards_1 = require("../utils/type-guards");
const workflow_collaboration_validation_1 = require("./workflow-collaboration.validation");
class WorkflowCollaborationController {
    constructor(workflowCollaborationService) {
        this.workflowCollaborationService = workflowCollaborationService;
        this.getAllWorkflows = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { status, page, size, sort } = req.query;
            const workflows = await this.workflowCollaborationService.getAllWorkflows((0, type_guards_1.getParam)(status), parseInt((0, type_guards_1.getParam)(page, '1')), parseInt((0, type_guards_1.getParam)(size, '10')), (0, type_guards_1.getParam)(sort));
            res.status(200).json(workflows);
        });
        this.createWorkflow = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const workflowData = req.body;
            const newWorkflow = await this.workflowCollaborationService.createWorkflow(workflowData);
            res.status(201).json(newWorkflow);
        });
        this.getWorkflowById = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { workflowId } = req.params;
            const workflow = await this.workflowCollaborationService.getWorkflowById((0, type_guards_1.requireParam)(workflowId, 'workflowId'));
            res.status(200).json(workflow);
        });
        this.updateWorkflow = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { workflowId } = req.params;
            const workflowData = req.body;
            const updatedWorkflow = await this.workflowCollaborationService.updateWorkflow((0, type_guards_1.requireParam)(workflowId, 'workflowId'), workflowData);
            res.status(200).json(updatedWorkflow);
        });
        this.deleteWorkflow = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { workflowId } = req.params;
            await this.workflowCollaborationService.deleteWorkflow((0, type_guards_1.requireParam)(workflowId, 'workflowId'));
            res.status(204).send();
        });
        this.getAllTasks = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { status, assignedTo, page, size, sort } = req.query;
            const tasks = await this.workflowCollaborationService.getAllTasks((0, type_guards_1.getParam)(status), (0, type_guards_1.getParam)(assignedTo), parseInt((0, type_guards_1.getParam)(page, '1')), parseInt((0, type_guards_1.getParam)(size, '10')), (0, type_guards_1.getParam)(sort));
            res.status(200).json(tasks);
        });
        this.createTask = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const taskData = req.body;
            const newTask = await this.workflowCollaborationService.createTask(taskData);
            res.status(201).json(newTask);
        });
        this.getTaskById = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { taskId } = req.params;
            const task = await this.workflowCollaborationService.getTaskById((0, type_guards_1.requireParam)(taskId, 'taskId'));
            res.status(200).json(task);
        });
        this.updateTask = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { taskId } = req.params;
            const taskData = req.body;
            const updatedTask = await this.workflowCollaborationService.updateTask((0, type_guards_1.requireParam)(taskId, 'taskId'), taskData);
            res.status(200).json(updatedTask);
        });
        this.deleteTask = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { taskId } = req.params;
            await this.workflowCollaborationService.deleteTask((0, type_guards_1.requireParam)(taskId, 'taskId'));
            res.status(204).send();
        });
        this.startCollaborationSession = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const sessionData = req.body;
            const newSession = await this.workflowCollaborationService.startCollaborationSession(sessionData);
            res.status(201).json(newSession);
        });
        this.joinCollaborationSession = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { sessionId } = req.params;
            const { userId } = req.body;
            const updatedSession = await this.workflowCollaborationService.joinCollaborationSession((0, type_guards_1.requireParam)(sessionId, 'sessionId'), (0, type_guards_1.requireParam)(userId, 'userId'));
            res.status(200).json(updatedSession);
        });
        this.leaveCollaborationSession = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { sessionId } = req.params;
            const { userId } = req.body;
            await this.workflowCollaborationService.leaveCollaborationSession((0, type_guards_1.requireParam)(sessionId, 'sessionId'), (0, type_guards_1.requireParam)(userId, 'userId'));
            res.status(204).send();
        });
        this.sendCrossTenantMessage = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const messageData = req.body;
            const result = await this.workflowCollaborationService.sendCrossTenantMessage(messageData);
            res.status(200).json(result);
        });
        this.getAllNotifications = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { userId, read, page, size, sort } = req.query;
            const notifications = await this.workflowCollaborationService.getAllNotifications((0, type_guards_1.getParam)(userId), (0, type_guards_1.getParam)(read), parseInt((0, type_guards_1.getParam)(page, '1')), parseInt((0, type_guards_1.getParam)(size, '10')), (0, type_guards_1.getParam)(sort));
            res.status(200).json(notifications);
        });
        this.markNotificationAsRead = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { notificationId } = req.params;
            await this.workflowCollaborationService.markNotificationAsRead((0, type_guards_1.requireParam)(notificationId, 'notificationId'));
            res.status(204).send();
        });
        this.shareWorkflow = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const shareData = req.body;
            const result = await this.workflowCollaborationService.shareWorkflow(shareData);
            res.status(200).json(result);
        });
        this.deleteWorkflowShare = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { workflowId, permissionId } = req.params;
            await this.workflowCollaborationService.deleteWorkflowShare((0, type_guards_1.requireParam)(workflowId, 'workflowId'), (0, type_guards_1.requireParam)(permissionId, 'permissionId'));
            res.status(204).send();
        });
        this.delegateTask = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { taskId } = req.params;
            const delegateData = req.body;
            const result = await this.workflowCollaborationService.delegateTask((0, type_guards_1.requireParam)(taskId, 'taskId'), delegateData);
            res.status(200).json(result);
        });
        this.getAllWorkspaces = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { ownerId, paimInstanceId, page, size, sort } = req.query;
            const workspaces = await this.workflowCollaborationService.getAllWorkspaces((0, type_guards_1.getParam)(ownerId), (0, type_guards_1.getParam)(paimInstanceId), parseInt((0, type_guards_1.getParam)(page, '1')), parseInt((0, type_guards_1.getParam)(size, '10')), (0, type_guards_1.getParam)(sort));
            res.status(200).json(workspaces);
        });
        this.createWorkspace = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const workspaceData = req.body;
            const newWorkspace = await this.workflowCollaborationService.createWorkspace(workspaceData);
            res.status(201).json(newWorkspace);
        });
        this.getWorkspaceById = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { workspaceId } = req.params;
            const workspace = await this.workflowCollaborationService.getWorkspaceById((0, type_guards_1.requireParam)(workspaceId, 'workspaceId'));
            res.status(200).json(workspace);
        });
        this.updateWorkspace = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { workspaceId } = req.params;
            const workspaceData = req.body;
            const updatedWorkspace = await this.workflowCollaborationService.updateWorkspace((0, type_guards_1.requireParam)(workspaceId, 'workspaceId'), workspaceData);
            res.status(200).json(updatedWorkspace);
        });
        this.deleteWorkspace = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { workspaceId } = req.params;
            await this.workflowCollaborationService.deleteWorkspace((0, type_guards_1.requireParam)(workspaceId, 'workspaceId'));
            res.status(204).send();
        });
        this.getAllTeams = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { paimInstanceId, page, size, sort } = req.query;
            const teams = await this.workflowCollaborationService.getAllTeams((0, type_guards_1.getParam)(paimInstanceId), parseInt((0, type_guards_1.getParam)(page, '1')), parseInt((0, type_guards_1.getParam)(size, '10')), (0, type_guards_1.getParam)(sort));
            res.status(200).json(teams);
        });
        this.createTeam = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const teamData = req.body;
            const newTeam = await this.workflowCollaborationService.createTeam(teamData);
            res.status(201).json(newTeam);
        });
        this.getTeamById = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { teamId } = req.params;
            const team = await this.workflowCollaborationService.getTeamById((0, type_guards_1.requireParam)(teamId, 'teamId'));
            res.status(200).json(team);
        });
        this.updateTeam = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { teamId } = req.params;
            const teamData = req.body;
            const updatedTeam = await this.workflowCollaborationService.updateTeam((0, type_guards_1.requireParam)(teamId, 'teamId'), teamData);
            res.status(200).json(updatedTeam);
        });
        this.deleteTeam = (0, asyncHandler_1.asyncHandler)(async (req, res) => {
            const { teamId } = req.params;
            await this.workflowCollaborationService.deleteTeam((0, type_guards_1.requireParam)(teamId, 'teamId'));
            res.status(204).send();
        });
        this.router = express.Router();
        this.initializeRoutes();
    }
    initializeRoutes() {
        this.router.get('/', (0, validation_1.validate)(workflow_collaboration_validation_1.getAllWorkflowsQuerySchema), this.getAllWorkflows);
        this.router.post('/', (0, validation_1.validate)(workflow_collaboration_validation_1.createWorkflowSchema), this.createWorkflow);
        this.router.get('/:workflowId', this.getWorkflowById);
        this.router.put('/:workflowId', (0, validation_1.validate)(workflow_collaboration_validation_1.updateWorkflowSchema), this.updateWorkflow);
        this.router.delete('/:workflowId', this.deleteWorkflow);
        this.router.get('/tasks', (0, validation_1.validate)(workflow_collaboration_validation_1.getAllTasksQuerySchema), this.getAllTasks);
        this.router.post('/tasks', (0, validation_1.validate)(workflow_collaboration_validation_1.createTaskSchema), this.createTask);
        this.router.get('/tasks/:taskId', this.getTaskById);
        this.router.put('/tasks/:taskId', (0, validation_1.validate)(workflow_collaboration_validation_1.updateTaskSchema), this.updateTask);
        this.router.delete('/tasks/:taskId', this.deleteTask);
        this.router.post('/collaboration/sessions', (0, validation_1.validate)(workflow_collaboration_validation_1.startCollaborationSessionSchema), this.startCollaborationSession);
        this.router.post('/collaboration/sessions/:sessionId/join', (0, validation_1.validate)(workflow_collaboration_validation_1.userIdBodySchema), this.joinCollaborationSession);
        this.router.post('/collaboration/sessions/:sessionId/leave', (0, validation_1.validate)(workflow_collaboration_validation_1.userIdBodySchema), this.leaveCollaborationSession);
        this.router.post('/cross-tenant-communication/messages', (0, validation_1.validate)(workflow_collaboration_validation_1.crossTenantMessageSchema), this.sendCrossTenantMessage);
        this.router.get('/notifications', (0, validation_1.validate)(workflow_collaboration_validation_1.getAllNotificationsQuerySchema), this.getAllNotifications);
        this.router.post('/notifications/:notificationId/read', this.markNotificationAsRead);
        this.router.post('/workflow-sharing/share', (0, validation_1.validate)(workflow_collaboration_validation_1.shareWorkflowSchema), this.shareWorkflow);
        this.router.delete('/workflow-sharing/share/:workflowId/:permissionId', this.deleteWorkflowShare);
        this.router.post('/task-delegation/delegate/:taskId', (0, validation_1.validate)(workflow_collaboration_validation_1.delegateTaskSchema), this.delegateTask);
        this.router.get('/collaborative-workspaces', (0, validation_1.validate)(workflow_collaboration_validation_1.getAllWorkspacesQuerySchema), this.getAllWorkspaces);
        this.router.post('/collaborative-workspaces', (0, validation_1.validate)(workflow_collaboration_validation_1.createWorkspaceSchema), this.createWorkspace);
        this.router.get('/collaborative-workspaces/:workspaceId', this.getWorkspaceById);
        this.router.put('/collaborative-workspaces/:workspaceId', (0, validation_1.validate)(workflow_collaboration_validation_1.updateWorkspaceSchema), this.updateWorkspace);
        this.router.delete('/collaborative-workspaces/:workspaceId', this.deleteWorkspace);
        this.router.get('/team-coordination/teams', (0, validation_1.validate)(workflow_collaboration_validation_1.getAllTeamsQuerySchema), this.getAllTeams);
        this.router.post('/team-coordination/teams', (0, validation_1.validate)(workflow_collaboration_validation_1.createTeamSchema), this.createTeam);
        this.router.get('/team-coordination/teams/:teamId', this.getTeamById);
        this.router.put('/team-coordination/teams/:teamId', (0, validation_1.validate)(workflow_collaboration_validation_1.updateTeamSchema), this.updateTeam);
        this.router.delete('/team-coordination/teams/:teamId', this.deleteTeam);
    }
}
exports.WorkflowCollaborationController = WorkflowCollaborationController;
