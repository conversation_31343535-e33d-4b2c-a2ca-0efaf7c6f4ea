"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.databaseAdapter = void 0;
const knex_1 = __importDefault(require("knex"));
const knex_config_1 = __importDefault(require("../config/knex.config"));
const adapter_factory_1 = require("../database/adapters/adapter-factory");
const db = (0, knex_1.default)(knex_config_1.default[process.env.NODE_ENV || 'development']);
class DatabaseAdapter {
    constructor(adapter) {
        this.db = adapter || adapter_factory_1.AdapterFactory.getAdapter('appwrite');
    }
    async getUser(id) {
        console.log(`[DatabaseAdapter] getUser START - id: ${id}`);
        const user = await this.db.getUserById(id);
        console.log(`[DatabaseAdapter] getUser END - id: ${id}, found: ${!!user}`);
        return user;
    }
    async getUserByEmail(email) {
        console.log(`[DatabaseAdapter] getUserByEmail START - email: ${email}`);
        const user = await this.db.getUserByEmail(email);
        console.log(`[DatabaseAdapter] getUserByEmail END - email: ${email}, found: ${!!user}`);
        return user;
    }
    async getTenantCount() {
        console.log(`[DatabaseAdapter] getTenantCount START`);
        const response = await this.db.listDocuments('tenants', []);
        console.log(`[DatabaseAdapter] getTenantCount END - count: ${response.length}`);
        return response.length;
    }
    async createTenant(tenant_name, schema_name, founder_club_flag, promo_start_date, promo_end_date, org_seats_allowed, org_seats_used) {
        console.log(`[DatabaseAdapter] createTenant START - name: ${tenant_name}`);
        const data = await this.db.createDocument('tenants', {
            tenant_name,
            schema_name,
            founder_club_flag,
            promo_start_date: promo_start_date?.toISOString(),
            promo_end_date: promo_end_date?.toISOString(),
            org_seats_allowed,
            org_seats_used,
        });
        console.log(`[DatabaseAdapter] createTenant END - name: ${tenant_name}, id: ${data?.$id}`);
        return data;
    }
    async getRoleByName(tenantId, roleName) {
        console.log(`[DatabaseAdapter] getRoleByName START - tenant: ${tenantId}, role: ${roleName}`);
        const roles = await this.db.listDocuments('roles', [
            `tenant_id=${tenantId}`,
            `role_name=${roleName}`
        ]);
        const data = roles.length > 0 ? roles[0] : null;
        console.log(`[DatabaseAdapter] getRoleByName END - tenant: ${tenantId}, role: ${roleName}, found: ${!!data}`);
        return data;
    }
    async createRole(tenantId, roleName, description) {
        console.log(`[DatabaseAdapter] createRole START - tenant: ${tenantId}, role: ${roleName}`);
        const data = await this.db.createDocument('roles', {
            tenant_id: tenantId,
            role_name: roleName,
            description,
        });
        console.log(`[DatabaseAdapter] createRole END - tenant: ${tenantId}, role: ${roleName}, id: ${data?.$id}`);
        return data;
    }
    async createUser(tenantId, email, passwordHash, firstName, lastName, paimTier) {
        console.log(`[DatabaseAdapter] createUser START - email: ${email}, tenant: ${tenantId}`);
        const data = await this.db.createUser({
            email,
            password: passwordHash,
            name: `${firstName} ${lastName}`,
            prefs: {
                tenant_id: tenantId,
                paim_tier: paimTier,
                first_name: firstName,
                last_name: lastName,
            }
        });
        console.log(`[DatabaseAdapter] createUser END - email: ${email}, id: ${data?.$id}`);
        return data;
    }
    async assignUserRole(userId, roleId) {
        console.log(`[DatabaseAdapter] assignUserRole START - user: ${userId}, role: ${roleId}`);
        await this.db.createDocument('user_roles', {
            user_id: userId,
            role_id: roleId,
        });
        console.log(`[DatabaseAdapter] assignUserRole END - user: ${userId}, role: ${roleId}`);
    }
    async getUserRoles(userId) {
        console.log(`[DatabaseAdapter] getUserRoles START - user: ${userId}`);
        const userRoles = await this.db.listDocuments('user_roles', [
            `user_id=${userId}`
        ]);
        const roles = [];
        for (const userRole of userRoles) {
            const role = await this.db.getDocument('roles', userRole.role_id);
            if (role) {
                roles.push(role);
            }
        }
        console.log(`[DatabaseAdapter] getUserRoles END - user: ${userId}, roles: ${roles.length}`);
        return roles;
    }
    async getUserProfile(userId) {
        const profile = await this.db.getDocument('user_profiles', userId);
        const user = await this.db.getUserById(userId);
        if (!profile || !user) {
            return null;
        }
        return {
            ...profile,
            users: {
                email: user.email,
                first_name: user.prefs?.first_name,
                last_name: user.prefs?.last_name,
            }
        };
    }
    async updateUserXP(userId, tenantId, xpGained, eventType, description) {
        const profiles = await this.db.listDocuments('user_profiles', [
            `user_id=${userId}`,
            `tenant_id=${tenantId}`
        ]);
        const profile = profiles.length > 0 ? profiles[0] : null;
        if (!profile) {
            throw new Error('User profile not found');
        }
        const newXP = profile.xp + xpGained;
        const newTotalXP = profile.total_xp + xpGained;
        await this.db.updateDocument('user_profiles', profile.$id, {
            xp: newXP,
            total_xp: newTotalXP,
            last_activity: new Date().toISOString()
        });
        await this.db.createDocument('xp_events', {
            user_id: userId,
            tenant_id: tenantId,
            event_type: eventType,
            xp_gained: xpGained,
            description: description
        });
        return { newXP, newTotalXP };
    }
    async getComplexBillingReport(tenantId, startDate, endDate) {
        return await db.raw(`
      SELECT
        u.email,
        u.first_name,
        u.last_name,
        SUM(bu.usage_amount) as total_usage,
        SUM(bu.cost) as total_cost,
        COUNT(bu.id) as usage_events
      FROM users u
      LEFT JOIN billing_usage bu ON u.id = bu.user_id
      WHERE u.tenant_id = ?
        AND bu.billing_period_start >= ?
        AND bu.billing_period_end <= ?
      GROUP BY u.id, u.email, u.first_name, u.last_name
      ORDER BY total_cost DESC
    `, [tenantId, startDate, endDate]);
    }
}
exports.databaseAdapter = new DatabaseAdapter();
