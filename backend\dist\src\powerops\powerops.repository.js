"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PowerOpsRepository = void 0;
const powerops_types_1 = require("./powerops.types");
class PowerOpsRepository {
    constructor(db) {
        this.TABLE_POWER_OPS_USAGE = 'power_ops_usage';
        this.TABLE_XP = 'xp';
        this.TABLE_XP_EVENTS = 'xp_events';
        this.TABLE_BADGES = 'badges';
        this.TABLE_USER_BADGES = 'user_badges';
        this.TABLE_ACHIEVEMENTS = 'achievements';
        this.TABLE_STREAKS = 'streaks';
        this.TABLE_BUDGETS = 'budgets';
        this.TABLE_INVOICES = 'invoices';
        this.TABLE_PAYMENTS = 'payments';
        this.TABLE_LEADERBOARD = 'leaderboard';
        this.TABLE_COST_OPTIMIZATION_RECOMMENDATIONS = 'cost_optimization_recommendations';
        this.TABLE_RESOURCE_USAGE_LIMITS = 'resource_usage_limits';
        this.TABLE_NOTIFICATIONS = 'notifications';
        this.db = db;
    }
    async logPowerOpsUsage(data) {
        const [id] = await this.db(this.TABLE_POWER_OPS_USAGE).insert({
            entity_id: data.entityId,
            entity_type: data.entityType,
            usage_units: data.usageUnits,
            cost_category: data.costCategory,
            estimated_cost: data.usageUnits * this.getCostPerUnit(data.costCategory),
            description: data.description,
            timestamp: data.timestamp,
        }).returning('id');
        const entry = await this.getPowerOpsUsageEntryById(id);
        if (!entry) {
            throw new Error('Failed to retrieve PowerOps usage entry after creation.');
        }
        return entry;
    }
    async getPowerOpsUsage(entityId, entityType, startDate, endDate) {
        let query = this.db(this.TABLE_POWER_OPS_USAGE)
            .where({ entity_id: entityId, entity_type: entityType });
        if (startDate) {
            query = query.andWhere('timestamp', '>=', startDate);
        }
        if (endDate) {
            query = query.andWhere('timestamp', '<=', endDate);
        }
        return query.select('*');
    }
    async getPowerOpsUsageEntryById(id) {
        return this.db(this.TABLE_POWER_OPS_USAGE).where({ id }).first();
    }
    async getXp(entityId, entityType) {
        const xpDb = await this.db(this.TABLE_XP)
            .where({ entity_id: entityId, entity_type: entityType })
            .select('entity_id', 'entity_type', 'current_xp', 'last_awarded_at')
            .first();
        if (!xpDb) {
            return undefined;
        }
        return this.mapDbToXp(xpDb);
    }
    async awardXp(data) {
        const { entityId, entityType, powerops, reason, metadata } = data;
        const currentTimestamp = new Date().toISOString();
        await this.db(this.TABLE_XP_EVENTS).insert({
            user_id: entityId,
            amount: powerops,
            reason: reason,
            metadata: metadata ? JSON.stringify(metadata) : null,
            created_at: currentTimestamp,
        });
        let xpEntry = await this.getXp(entityId, entityType);
        let newXpAmount = powerops;
        if (xpEntry) {
            newXpAmount = xpEntry.currentXp + powerops;
            await this.db(this.TABLE_XP)
                .where({ entity_id: entityId, entity_type: entityType })
                .update({
                current_xp: newXpAmount,
                level: this.calculateLevel(newXpAmount),
                last_awarded_at: currentTimestamp,
            });
        }
        else {
            await this.db(this.TABLE_XP).insert({
                entity_id: entityId,
                entity_type: entityType,
                current_xp: newXpAmount,
                level: this.calculateLevel(newXpAmount),
                last_awarded_at: currentTimestamp,
                created_at: currentTimestamp,
                updated_at: currentTimestamp,
            });
        }
        const updatedXp = await this.getXp(entityId, entityType);
        if (!updatedXp) {
            throw new Error('Failed to retrieve XP after awarding.');
        }
        return updatedXp;
    }
    async logXpEvent(xpEvent) {
        await this.db(this.TABLE_XP_EVENTS).insert({
            user_id: xpEvent.org_id,
            amount: xpEvent.xp_gained,
            reason: xpEvent.reason,
            metadata: xpEvent.metadata ? JSON.stringify(xpEvent.metadata) : null,
            created_at: xpEvent.timestamp || new Date(),
        });
    }
    async getAllBadges() {
        return this.db(this.TABLE_BADGES).select('*');
    }
    async getBadgesForEntity(entityId, entityType) {
        return this.db(this.TABLE_USER_BADGES)
            .join(this.TABLE_BADGES, `${this.TABLE_USER_BADGES}.badge_id`, '=', `${this.TABLE_BADGES}.badge_id`)
            .where(`${this.TABLE_USER_BADGES}.user_id`, entityId)
            .select(`${this.TABLE_BADGES}.*`, `${this.TABLE_USER_BADGES}.earned_at`);
    }
    async awardBadge(data) {
        const [id] = await this.db(this.TABLE_USER_BADGES).insert({
            user_id: data.entityId,
            badge_id: data.badgeId,
            earned_at: new Date().toISOString(),
        }).returning('user_badge_id');
        const badge = await this.db(this.TABLE_BADGES).where({ badge_id: data.badgeId }).first();
        if (!badge) {
            throw new Error('Failed to retrieve badge after awarding.');
        }
        return badge;
    }
    async getAchievements(entityId, entityType) {
        return this.db(this.TABLE_ACHIEVEMENTS).where({ entity_id: entityId, entity_type: entityType }).select('*');
    }
    async grantAchievement(data) {
        const [id] = await this.db(this.TABLE_ACHIEVEMENTS).insert({
            entity_id: data.entityId,
            entity_type: data.entityType,
            achievement_id: data.achievementId,
            unlocked_at: new Date().toISOString(),
        }).returning('id');
        const achievement = await this.db(this.TABLE_ACHIEVEMENTS).where({ id: id }).first();
        if (!achievement) {
            throw new Error('Failed to retrieve achievement after creation.');
        }
        return achievement;
    }
    async getStreaks(entityId, entityType) {
        return this.db(this.TABLE_STREAKS).where({ entity_id: entityId, entity_type: entityType }).select('*');
    }
    async getBudgets(entityId, entityType) {
        return this.db(this.TABLE_BUDGETS).where({ entity_id: entityId, entity_type: entityType }).select('*');
    }
    async getBudgetById(budgetId) {
        return this.db(this.TABLE_BUDGETS).where({ id: budgetId }).first();
    }
    async createBudget(data) {
        const [id] = await this.db(this.TABLE_BUDGETS).insert({
            entity_id: data.entityId,
            entity_type: data.entityType,
            monthly_limit: data.monthlyLimit,
            currency: data.currency,
            alert_threshold: data.alertThreshold,
            current_spend: 0,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
        }).returning('id');
        const budget = await this.db(this.TABLE_BUDGETS).where({ id: id }).first();
        if (!budget) {
            throw new Error('Failed to retrieve budget after creation.');
        }
        return budget;
    }
    async updateBudget(budgetId, data) {
        await this.db(this.TABLE_BUDGETS)
            .where({ id: budgetId })
            .update({
            monthly_limit: data.monthlyLimit,
            alert_threshold: data.alertThreshold,
            updated_at: new Date().toISOString(),
        });
        return this.db(this.TABLE_BUDGETS).where({ id: budgetId }).first();
    }
    async deleteBudget(budgetId) {
        const deletedCount = await this.db(this.TABLE_BUDGETS).where({ id: budgetId }).del();
        return deletedCount > 0;
    }
    async getInvoices(entityId, entityType) {
        return this.db(this.TABLE_INVOICES).where({ entity_id: entityId, entity_type: entityType }).select('*');
    }
    async createInvoice(data) {
        const [id] = await this.db(this.TABLE_INVOICES).insert({
            entity_id: data.entityId,
            entity_type: data.entityType,
            billing_period_start: data.billingPeriodStart,
            billing_period_end: data.billingPeriodEnd,
            total_amount: data.lineItems.reduce((sum, item) => sum + item.total, 0),
            currency: 'USD',
            status: 'pending',
            issue_date: new Date().toISOString(),
            due_date: new Date(new Date().setDate(new Date().getDate() + 30)).toISOString(),
            payment_method_id: data.paymentMethodId,
            line_items: JSON.stringify(data.lineItems),
        }).returning('id');
        const invoice = await this.db(this.TABLE_INVOICES).where({ id: id }).first();
        if (!invoice) {
            throw new Error('Failed to retrieve invoice after creation.');
        }
        return invoice;
    }
    async processPayment(data) {
        const [id] = await this.db(this.TABLE_PAYMENTS).insert({
            invoice_id: data.invoiceId,
            amount: data.amount,
            currency: data.currency,
            payment_method: data.paymentMethod,
            transaction_id: 'mock_transaction_id_' + Date.now(),
            status: 'success',
            timestamp: new Date().toISOString(),
        }).returning('id');
        await this.db(this.TABLE_INVOICES)
            .where({ id: data.invoiceId })
            .update({ status: 'paid' });
        const payment = await this.db(this.TABLE_PAYMENTS).where({ id: id }).first();
        if (!payment) {
            throw new Error('Failed to retrieve payment after creation.');
        }
        return payment;
    }
    async getLeaderboard(metric, limit = 10) {
        return this.db(this.TABLE_LEADERBOARD)
            .orderBy('score', 'desc')
            .limit(limit)
            .select('*');
    }
    async getCostOptimizationRecommendations(entityId, entityType) {
        return this.db(this.TABLE_COST_OPTIMIZATION_RECOMMENDATIONS).where({ entity_id: entityId, entity_type: entityType }).select('*');
    }
    async getResourceUsageLimits(entityId, entityType) {
        return this.db(this.TABLE_RESOURCE_USAGE_LIMITS).where({ entity_id: entityId, entity_type: entityType }).select('*');
    }
    async setResourceUsageLimit(data) {
        const [id] = await this.db(this.TABLE_RESOURCE_USAGE_LIMITS).insert({
            entity_id: data.entityId,
            entity_type: data.entityType,
            resource_type: data.resourceType,
            limit: data.limit,
            unit: data.unit,
            current_usage: 0,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
        }).returning('id');
        const resourceUsageLimit = await this.db(this.TABLE_RESOURCE_USAGE_LIMITS).where({ id: id }).first();
        if (!resourceUsageLimit) {
            throw new Error('Failed to retrieve resource usage limit after creation.');
        }
        return resourceUsageLimit;
    }
    async getNotifications(entityId, entityType) {
        return this.db(this.TABLE_NOTIFICATIONS).where({ entity_id: entityId, entity_type: entityType }).select('*');
    }
    async createNotification(data) {
        const [id] = await this.db(this.TABLE_NOTIFICATIONS).insert({
            entity_id: data.entityId,
            entity_type: data.entityType,
            type: data.type,
            message: data.message,
            read: false,
            timestamp: new Date().toISOString(),
        }).returning('id');
        const notification = await this.db(this.TABLE_NOTIFICATIONS).where({ id: id }).first();
        if (!notification) {
            throw new Error('Failed to retrieve notification after creation.');
        }
        return notification;
    }
    getCostPerUnit(category) {
        switch (category) {
            case powerops_types_1.CostCategory.Compute: return 0.01;
            case powerops_types_1.CostCategory.Storage: return 0.001;
            case powerops_types_1.CostCategory.Bandwidth: return 0.005;
            case powerops_types_1.CostCategory.AIModel: return 0.05;
            default: return 0.01;
        }
    }
    calculateLevel(xp) {
        return Math.floor(xp / 1000) + 1;
    }
    mapDbToXp(dbRow) {
        return {
            entityId: dbRow.entity_id,
            entityType: dbRow.entity_type,
            currentXp: dbRow.current_xp,
            level: dbRow.level,
            lastAwardedAt: dbRow.last_awarded_at,
        };
    }
    mapDbToBadge(dbRow) {
        return {
            id: dbRow.badge_id,
            name: dbRow.name,
            description: dbRow.description,
            imageUrl: dbRow.image_url,
            awardedAt: dbRow.earned_at,
        };
    }
}
exports.PowerOpsRepository = PowerOpsRepository;
