"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateAgentPermission = exports.agentMiddleware = void 0;
const logger_1 = __importDefault(require("../config/logger"));
const agentMiddleware = (req, res, next) => {
    logger_1.default.info(`Agent Middleware: ${req.method} ${req.originalUrl}`);
    next();
};
exports.agentMiddleware = agentMiddleware;
const validateAgentPermission = (permission) => {
    return (req, res, next) => {
        const tenantId = req.headers['x-tenant-id'] || 'mock-tenant-id';
        const userId = req.user?.id || 'mock-user-id';
        logger_1.default.debug(`Checking permission '${permission}' for user '${userId}' in tenant '${tenantId}'`);
        next();
    };
};
exports.validateAgentPermission = validateAgentPermission;
