"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ROLE_HIERARCHY = exports.ROLE_PERMISSIONS = exports.UserRole = void 0;
const permissions_1 = require("./permissions");
var UserRole;
(function (UserRole) {
    UserRole["SuperAdmin"] = "SuperAdmin";
    UserRole["OrgAdmin"] = "OrgAdmin";
    UserRole["PAIMManager"] = "PAIMManager";
    UserRole["TeamLead"] = "TeamLead";
    UserRole["Member"] = "Member";
    UserRole["Guest"] = "Guest";
})(UserRole || (exports.UserRole = UserRole = {}));
exports.ROLE_PERMISSIONS = {
    [UserRole.SuperAdmin]: [
        permissions_1.PERMISSIONS.PAIM_CREATE,
        permissions_1.PERMISSIONS.PAIM_READ,
        permissions_1.PERMISSIONS.PAIM_UPDATE,
        permissions_1.PERMISSIONS.PAIM_DELETE,
        permissions_1.PERMISSIONS.PAIM_MANAGE_TIERS,
        permissions_1.PERMISSIONS.PAIM_VIEW_ALL,
        permissions_1.PERMISSIONS.POWER_OPS_CREATE,
        permissions_1.PERMISSIONS.POWER_OPS_READ,
        permissions_1.PERMISSIONS.POWER_OPS_UPDATE,
        permissions_1.PERMISSIONS.POWER_OPS_DELETE,
        permissions_1.PERMISSIONS.POWER_OPS_MANAGE_ALL,
        permissions_1.PERMISSIONS.WORKFLOW_CREATE,
        permissions_1.PERMISSIONS.WORKFLOW_READ,
        permissions_1.PERMISSIONS.WORKFLOW_UPDATE,
        permissions_1.PERMISSIONS.WORKFLOW_DELETE,
        permissions_1.PERMISSIONS.WORKFLOW_MANAGE_ALL,
        permissions_1.PERMISSIONS.AGENT_CREATE,
        permissions_1.PERMISSIONS.AGENT_READ,
        permissions_1.PERMISSIONS.AGENT_UPDATE,
        permissions_1.PERMISSIONS.AGENT_DELETE,
        permissions_1.PERMISSIONS.AGENT_MANAGE_ALL,
        permissions_1.PERMISSIONS.ORG_CREATE,
        permissions_1.PERMISSIONS.ORG_READ,
        permissions_1.PERMISSIONS.ORG_UPDATE,
        permissions_1.PERMISSIONS.ORG_DELETE,
        permissions_1.PERMISSIONS.ORG_MANAGE_MEMBERS,
        permissions_1.PERMISSIONS.ORG_MANAGE_ROLES,
        permissions_1.PERMISSIONS.ORG_VIEW_ALL,
        permissions_1.PERMISSIONS.BILLING_VIEW,
        permissions_1.PERMISSIONS.BILLING_MANAGE,
        permissions_1.PERMISSIONS.AUDIT_VIEW,
        permissions_1.PERMISSIONS.AUDIT_MANAGE,
        permissions_1.PERMISSIONS.SYSTEM_ADMIN_ACCESS,
        permissions_1.PERMISSIONS.SYSTEM_VIEW_LOGS,
        permissions_1.PERMISSIONS.SYSTEM_MANAGE_USERS,
        permissions_1.PERMISSIONS.SYSTEM_MANAGE_SETTINGS,
    ],
    [UserRole.OrgAdmin]: [
        permissions_1.PERMISSIONS.PAIM_CREATE,
        permissions_1.PERMISSIONS.PAIM_READ,
        permissions_1.PERMISSIONS.PAIM_UPDATE,
        permissions_1.PERMISSIONS.PAIM_DELETE,
        permissions_1.PERMISSIONS.PAIM_MANAGE_TIERS,
        permissions_1.PERMISSIONS.POWER_OPS_CREATE,
        permissions_1.PERMISSIONS.POWER_OPS_READ,
        permissions_1.PERMISSIONS.POWER_OPS_UPDATE,
        permissions_1.PERMISSIONS.POWER_OPS_DELETE,
        permissions_1.PERMISSIONS.POWER_OPS_MANAGE_ALL,
        permissions_1.PERMISSIONS.WORKFLOW_CREATE,
        permissions_1.PERMISSIONS.WORKFLOW_READ,
        permissions_1.PERMISSIONS.WORKFLOW_UPDATE,
        permissions_1.PERMISSIONS.WORKFLOW_DELETE,
        permissions_1.PERMISSIONS.WORKFLOW_MANAGE_ALL,
        permissions_1.PERMISSIONS.AGENT_CREATE,
        permissions_1.PERMISSIONS.AGENT_READ,
        permissions_1.PERMISSIONS.AGENT_UPDATE,
        permissions_1.PERMISSIONS.AGENT_DELETE,
        permissions_1.PERMISSIONS.AGENT_MANAGE_ALL,
        permissions_1.PERMISSIONS.ORG_READ,
        permissions_1.PERMISSIONS.ORG_UPDATE,
        permissions_1.PERMISSIONS.ORG_MANAGE_MEMBERS,
        permissions_1.PERMISSIONS.ORG_MANAGE_ROLES,
        permissions_1.PERMISSIONS.BILLING_VIEW,
        permissions_1.PERMISSIONS.BILLING_MANAGE,
        permissions_1.PERMISSIONS.AUDIT_VIEW,
    ],
    [UserRole.PAIMManager]: [
        permissions_1.PERMISSIONS.PAIM_CREATE,
        permissions_1.PERMISSIONS.PAIM_READ,
        permissions_1.PERMISSIONS.PAIM_UPDATE,
        permissions_1.PERMISSIONS.PAIM_DELETE,
        permissions_1.PERMISSIONS.POWER_OPS_READ,
        permissions_1.PERMISSIONS.WORKFLOW_READ,
        permissions_1.PERMISSIONS.AGENT_READ,
        permissions_1.PERMISSIONS.ORG_READ,
    ],
    [UserRole.TeamLead]: [
        permissions_1.PERMISSIONS.PAIM_READ,
        permissions_1.PERMISSIONS.PAIM_UPDATE,
        permissions_1.PERMISSIONS.POWER_OPS_READ,
        permissions_1.PERMISSIONS.WORKFLOW_CREATE,
        permissions_1.PERMISSIONS.WORKFLOW_READ,
        permissions_1.PERMISSIONS.WORKFLOW_UPDATE,
        permissions_1.PERMISSIONS.AGENT_READ,
        permissions_1.PERMISSIONS.ORG_READ,
    ],
    [UserRole.Member]: [
        permissions_1.PERMISSIONS.PAIM_READ,
        permissions_1.PERMISSIONS.POWER_OPS_READ,
        permissions_1.PERMISSIONS.WORKFLOW_READ,
        permissions_1.PERMISSIONS.AGENT_READ,
        permissions_1.PERMISSIONS.ORG_READ,
    ],
    [UserRole.Guest]: [
        permissions_1.PERMISSIONS.PAIM_READ,
        permissions_1.PERMISSIONS.POWER_OPS_READ,
        permissions_1.PERMISSIONS.WORKFLOW_READ,
        permissions_1.PERMISSIONS.AGENT_READ,
        permissions_1.PERMISSIONS.ORG_READ,
    ],
};
exports.ROLE_HIERARCHY = {
    [UserRole.SuperAdmin]: [],
    [UserRole.OrgAdmin]: [UserRole.SuperAdmin],
    [UserRole.PAIMManager]: [UserRole.OrgAdmin],
    [UserRole.TeamLead]: [UserRole.PAIMManager],
    [UserRole.Member]: [UserRole.TeamLead],
    [UserRole.Guest]: [UserRole.Member],
};
