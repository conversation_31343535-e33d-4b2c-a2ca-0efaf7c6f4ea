"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CollaborationEvents = void 0;
const websocket_types_1 = require("../websocket.types");
class CollaborationEvents {
    constructor(wsService) {
        this.wsService = wsService;
        this.registerIncomingEventHandlers();
    }
    registerIncomingEventHandlers() {
        this.wsService.on(websocket_types_1.WebSocketEvent.CURSOR_UPDATE, (userId, payload) => {
            this.emitCursorUpdate(payload);
        });
        this.wsService.on(websocket_types_1.WebSocketEvent.PRESENCE_UPDATE, (userId, payload) => {
            this.emitPresenceUpdate(payload);
        });
        this.wsService.on(websocket_types_1.WebSocketEvent.EDIT_UPDATE, (userId, payload) => {
            this.emitEditUpdate(payload);
        });
    }
    emitWorkflowUpdate(payload) {
        this.wsService.broadcast(JSON.stringify({
            event: websocket_types_1.WebSocketEvent.COLLABORATION_UPDATE,
            payload,
        }));
    }
    emitCursorUpdate(payload) {
        this.wsService.broadcast(JSON.stringify({
            event: websocket_types_1.WebSocketEvent.CURSOR_UPDATE,
            payload,
        }));
    }
    emitPresenceUpdate(payload) {
        this.wsService.broadcast(JSON.stringify({
            event: websocket_types_1.WebSocketEvent.PRESENCE_UPDATE,
            payload,
        }));
    }
    emitEditUpdate(payload) {
        this.wsService.broadcast(JSON.stringify({
            event: websocket_types_1.WebSocketEvent.EDIT_UPDATE,
            payload,
        }));
    }
}
exports.CollaborationEvents = CollaborationEvents;
