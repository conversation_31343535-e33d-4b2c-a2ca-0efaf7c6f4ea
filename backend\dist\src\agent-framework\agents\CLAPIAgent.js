"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CLAPIAgent = void 0;
const Agent_1 = require("../core/Agent");
const logger_1 = __importDefault(require("../../config/logger"));
class CLAPIAgent extends Agent_1.Agent {
    constructor(config) {
        super('cl-api-agent', 'CL-API', 'core', config);
        this.requestCount = 0;
        this.lastResetTime = Date.now();
        this.rateLimitConfig = {
            enabled: this.config.get('rateLimit.enabled', true),
            requestsPerMinute: this.config.get('rateLimit.requestsPerMinute', 100),
            burstLimit: this.config.get('rateLimit.burstLimit', 10)
        };
        this.addCapability({
            name: 'api_request_handling',
            version: '1.0.0',
            description: 'Handle HTTP API requests',
            enabled: true
        });
        this.addCapability({
            name: 'rate_limiting',
            version: '1.0.0',
            description: 'Apply rate limiting to requests',
            enabled: this.rateLimitConfig.enabled
        });
        this.addCapability({
            name: 'response_formatting',
            version: '1.0.0',
            description: 'Format API responses',
            enabled: true
        });
        logger_1.default.info('CL-API Agent initialized with rate limiting configuration');
    }
    async executeTask(task) {
        switch (task.type) {
            case 'api_request':
                return await this.handleAPIRequest(task.payload);
            case 'format_response':
                return await this.formatResponse(task.payload);
            case 'check_rate_limit':
                return await this.checkRateLimit(task.payload.clientId);
            case 'get_api_status':
                return await this.getAPIStatus();
            default:
                return await super.executeTask(task);
        }
    }
    async handleAPIRequest(request) {
        logger_1.default.info(`Processing ${request.method} request to ${request.endpoint}`);
        try {
            if (this.rateLimitConfig.enabled) {
                const rateLimitCheck = await this.checkRateLimit('default');
                if (!rateLimitCheck.allowed) {
                    throw new Error(`Rate limit exceeded: ${rateLimitCheck.message}`);
                }
            }
            this.validateAPIRequest(request);
            const response = await this.processRequest(request);
            this.incrementRequestCount();
            logger_1.default.info(`API request processed successfully: ${request.method} ${request.endpoint}`);
            return response;
        }
        catch (error) {
            logger_1.default.error(`API request failed: ${error}`);
            throw error;
        }
    }
    validateAPIRequest(request) {
        if (!request.method) {
            throw new Error('HTTP method is required');
        }
        if (!request.endpoint) {
            throw new Error('API endpoint is required');
        }
        if (!['GET', 'POST', 'PUT', 'DELETE', 'PATCH'].includes(request.method)) {
            throw new Error(`Unsupported HTTP method: ${request.method}`);
        }
        if (!request.endpoint.startsWith('/')) {
            throw new Error('Endpoint must start with /');
        }
    }
    async processRequest(request) {
        const mockData = {
            message: `Mock response for ${request.method} ${request.endpoint}`,
            requestData: request.body || request.query,
            timestamp: new Date().toISOString()
        };
        return {
            status: 200,
            headers: {
                'Content-Type': 'application/json',
                'X-Agent-Id': this.id,
                'X-Request-Id': `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
            },
            data: mockData,
            timestamp: new Date().toISOString()
        };
    }
    async formatResponse(data) {
        const formattedResponse = {
            success: true,
            data: data,
            metadata: {
                agent: this.name,
                timestamp: new Date().toISOString(),
                version: '1.0.0'
            }
        };
        logger_1.default.debug('Response formatted by CL-API Agent');
        return formattedResponse;
    }
    async checkRateLimit(clientId) {
        if (!this.rateLimitConfig.enabled) {
            return {
                allowed: true,
                remaining: this.rateLimitConfig.requestsPerMinute,
                resetTime: this.lastResetTime + 60000
            };
        }
        const now = Date.now();
        const timeSinceReset = now - this.lastResetTime;
        if (timeSinceReset >= 60000) {
            this.requestCount = 0;
            this.lastResetTime = now;
        }
        const remaining = Math.max(0, this.rateLimitConfig.requestsPerMinute - this.requestCount);
        const allowed = this.requestCount < this.rateLimitConfig.requestsPerMinute;
        if (!allowed) {
            return {
                allowed: false,
                remaining: 0,
                resetTime: this.lastResetTime + 60000,
                message: `Rate limit of ${this.rateLimitConfig.requestsPerMinute} requests per minute exceeded`
            };
        }
        return {
            allowed: true,
            remaining,
            resetTime: this.lastResetTime + 60000
        };
    }
    async getAPIStatus() {
        const rateLimitStatus = await this.checkRateLimit('status');
        return {
            agent: {
                id: this.id,
                name: this.name,
                type: this.type,
                active: this.getIsActive()
            },
            rateLimit: {
                enabled: this.rateLimitConfig.enabled,
                requestsPerMinute: this.rateLimitConfig.requestsPerMinute,
                currentCount: this.requestCount,
                remaining: rateLimitStatus.remaining,
                resetTime: new Date(rateLimitStatus.resetTime).toISOString()
            },
            metrics: this.getMetrics(),
            capabilities: this.getCapabilities(),
            timestamp: new Date().toISOString()
        };
    }
    incrementRequestCount() {
        this.requestCount++;
    }
    updateRateLimitConfig(config) {
        this.rateLimitConfig = { ...this.rateLimitConfig, ...config };
        const rateLimitCapability = this.getCapabilities().find(c => c.name === 'rate_limiting');
        if (rateLimitCapability) {
            rateLimitCapability.enabled = this.rateLimitConfig.enabled;
        }
        logger_1.default.info('Rate limit configuration updated', this.rateLimitConfig);
    }
    getRateLimitConfig() {
        return { ...this.rateLimitConfig };
    }
    resetRateLimit() {
        this.requestCount = 0;
        this.lastResetTime = Date.now();
        logger_1.default.info('Rate limit counters reset');
    }
}
exports.CLAPIAgent = CLAPIAgent;
