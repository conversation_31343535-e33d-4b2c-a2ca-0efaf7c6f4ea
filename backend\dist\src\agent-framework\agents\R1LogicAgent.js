"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.R1LogicAgent = void 0;
const Agent_1 = require("../core/Agent");
const logger_1 = __importDefault(require("../../config/logger"));
class R1LogicAgent extends Agent_1.Agent {
    constructor(config) {
        super('r1-logic-agent', 'R1-Logic', 'core', config);
        this.businessRules = new Map();
        this.decisionCache = new Map();
        this.cacheTimeout = this.config.get('cache.timeout', 300000);
        this.addCapability({
            name: 'business_rule_processing',
            version: '1.0.0',
            description: 'Process business rules and logic',
            enabled: true
        });
        this.addCapability({
            name: 'decision_making',
            version: '1.0.0',
            description: 'Make decisions based on context and rules',
            enabled: true
        });
        this.addCapability({
            name: 'paim_integration',
            version: '1.0.0',
            description: 'Integration with PAIM system',
            enabled: true
        });
        this.addCapability({
            name: 'workflow_logic',
            version: '1.0.0',
            description: 'Process workflow logic and conditions',
            enabled: true
        });
        this.initializeDefaultRules();
        logger_1.default.info('R1-Logic Agent initialized with business rule processing');
    }
    async executeTask(task) {
        switch (task.type) {
            case 'process_business_logic':
                return await this.processBusinessLogic(task.payload);
            case 'make_decision':
                return await this.makeDecision(task.payload.context, task.payload.data);
            case 'validate_request':
                return await this.validateRequest(task.payload);
            case 'process_workflow_logic':
                return await this.processWorkflowLogic(task.payload);
            case 'add_business_rule':
                return await this.addBusinessRule(task.payload);
            case 'get_business_rules':
                return await this.getBusinessRules();
            default:
                return await super.executeTask(task);
        }
    }
    async processBusinessLogic(request) {
        logger_1.default.info(`Processing business logic: ${request.type}`);
        try {
            switch (request.type) {
                case 'business_rule':
                    return await this.applyBusinessRules(request.context, request.data, request.rules);
                case 'decision_making':
                    return await this.makeDecision(request.context, request.data);
                case 'validation':
                    return await this.validateRequest(request.data);
                case 'workflow_logic':
                    return await this.processWorkflowLogic(request.data);
                default:
                    throw new Error(`Unsupported logic processing type: ${request.type}`);
            }
        }
        catch (error) {
            logger_1.default.error(`Business logic processing failed: ${error}`);
            throw error;
        }
    }
    async applyBusinessRules(context, data, customRules) {
        const rulesToApply = customRules || Array.from(this.businessRules.values());
        const appliedRules = [];
        let decision = 'allow';
        let reason = 'No applicable rules found';
        const conditions = {};
        const sortedRules = rulesToApply
            .filter(rule => rule.enabled)
            .sort((a, b) => b.priority - a.priority);
        for (const rule of sortedRules) {
            try {
                const ruleResult = await this.evaluateRule(rule, context, data);
                if (ruleResult.applies) {
                    appliedRules.push(rule.id);
                    if (ruleResult.decision === 'deny') {
                        decision = 'deny';
                        reason = ruleResult.reason || `Rule ${rule.name} denied the request`;
                        break;
                    }
                    else if (ruleResult.decision === 'conditional') {
                        decision = 'conditional';
                        reason = ruleResult.reason || `Rule ${rule.name} requires conditions`;
                        Object.assign(conditions, ruleResult.conditions || {});
                    }
                }
            }
            catch (error) {
                logger_1.default.error(`Error evaluating rule ${rule.id}: ${error}`);
            }
        }
        const result = {
            decision,
            reason,
            appliedRules,
            conditions: Object.keys(conditions).length > 0 ? conditions : undefined,
            metadata: {
                evaluatedRules: sortedRules.length,
                context: context.tenantId,
                timestamp: new Date().toISOString()
            }
        };
        logger_1.default.info(`Business rules applied: ${decision} (${appliedRules.length} rules)`);
        return result;
    }
    async makeDecision(context, data) {
        const cacheKey = this.generateCacheKey(context, data);
        const cachedResult = this.decisionCache.get(cacheKey);
        if (cachedResult) {
            logger_1.default.debug('Decision retrieved from cache');
            return cachedResult;
        }
        const result = await this.applyBusinessRules(context, data);
        this.decisionCache.set(cacheKey, result);
        setTimeout(() => {
            this.decisionCache.delete(cacheKey);
        }, this.cacheTimeout);
        return result;
    }
    async evaluateRule(rule, context, data) {
        try {
            const ruleContext = {
                context,
                data,
                rule,
                hasPermission: (permission) => this.checkPermission(context, permission),
                isPaimTier: (tier) => context.paimTier === tier,
                isUser: (userId) => context.userId === userId,
                isTenant: (tenantId) => context.tenantId === tenantId
            };
            const conditionResult = this.evaluateCondition(rule.condition, ruleContext);
            if (!conditionResult) {
                return { applies: false };
            }
            const actionResult = this.executeAction(rule.action, ruleContext);
            return {
                applies: true,
                decision: actionResult.decision,
                reason: actionResult.reason,
                conditions: actionResult.conditions
            };
        }
        catch (error) {
            logger_1.default.error(`Rule evaluation error for ${rule.id}: ${error}`);
            return { applies: false };
        }
    }
    evaluateCondition(condition, context) {
        try {
            if (condition.includes('paimTier')) {
                const tierMatch = condition.match(/paimTier\s*===?\s*['"]([^'"]+)['"]/);
                if (tierMatch) {
                    return context.context.paimTier === tierMatch[1];
                }
            }
            if (condition.includes('userId')) {
                const userMatch = condition.match(/userId\s*===?\s*['"]([^'"]+)['"]/);
                if (userMatch) {
                    return context.context.userId === userMatch[1];
                }
            }
            return true;
        }
        catch (error) {
            logger_1.default.error(`Condition evaluation error: ${error}`);
            return false;
        }
    }
    executeAction(action, context) {
        if (action.includes('deny')) {
            return {
                decision: 'deny',
                reason: 'Action explicitly denies request'
            };
        }
        if (action.includes('conditional')) {
            return {
                decision: 'conditional',
                reason: 'Action requires additional conditions',
                conditions: { requiresApproval: true }
            };
        }
        return {
            decision: 'allow',
            reason: 'Action allows request'
        };
    }
    async validateRequest(data) {
        const errors = [];
        const warnings = [];
        if (!data) {
            errors.push('Request data is required');
        }
        if (data && typeof data === 'object') {
            const requiredFields = ['tenantId', 'userId'];
            for (const field of requiredFields) {
                if (!data[field]) {
                    errors.push(`Required field '${field}' is missing`);
                }
            }
        }
        return {
            valid: errors.length === 0,
            errors,
            warnings
        };
    }
    async processWorkflowLogic(data) {
        logger_1.default.info('Processing workflow logic');
        return {
            nextStep: 'approval_required',
            conditions: {
                requiresManagerApproval: true,
                estimatedTime: '2-3 business days'
            },
            metadata: {
                processedBy: this.name,
                timestamp: new Date().toISOString()
            }
        };
    }
    async addBusinessRule(rule) {
        if (this.businessRules.has(rule.id)) {
            throw new Error(`Business rule with ID '${rule.id}' already exists`);
        }
        this.businessRules.set(rule.id, rule);
        logger_1.default.info(`Business rule '${rule.name}' added`);
    }
    async getBusinessRules() {
        return Array.from(this.businessRules.values());
    }
    checkPermission(context, permission) {
        return true;
    }
    generateCacheKey(context, data) {
        const keyData = {
            userId: context.userId,
            tenantId: context.tenantId,
            paimTier: context.paimTier,
            dataHash: this.hashObject(data)
        };
        return Buffer.from(JSON.stringify(keyData)).toString('base64');
    }
    hashObject(obj) {
        return Buffer.from(JSON.stringify(obj)).toString('base64').substring(0, 16);
    }
    initializeDefaultRules() {
        const defaultRules = [
            {
                id: 'paim-tier-access',
                name: 'PAIM Tier Access Control',
                description: 'Control access based on PAIM tier',
                condition: 'paimTier !== "personal"',
                action: 'allow',
                priority: 100,
                enabled: true
            },
            {
                id: 'tenant-isolation',
                name: 'Tenant Isolation',
                description: 'Ensure tenant data isolation',
                condition: 'tenantId !== null',
                action: 'allow',
                priority: 200,
                enabled: true
            }
        ];
        defaultRules.forEach(rule => {
            this.businessRules.set(rule.id, rule);
        });
        logger_1.default.info(`Initialized ${defaultRules.length} default business rules`);
    }
    clearDecisionCache() {
        this.decisionCache.clear();
        logger_1.default.info('Decision cache cleared');
    }
    getCacheStats() {
        return {
            size: this.decisionCache.size,
            timeout: this.cacheTimeout
        };
    }
}
exports.R1LogicAgent = R1LogicAgent;
