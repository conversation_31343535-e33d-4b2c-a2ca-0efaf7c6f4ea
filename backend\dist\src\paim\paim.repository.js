"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PaimRepository = void 0;
const db_1 = __importDefault(require("../database/db"));
const paim_types_1 = require("./paim.types");
const uuid_1 = require("uuid");
const audit_types_1 = require("../audit/audit.types");
class PaimRepository {
    constructor(auditTrailService) {
        this.auditTrailService = auditTrailService;
        this.tableName = 'PaimInstances';
        this.paimTiersTableName = 'PaimTiers';
    }
    async createPaimInstance(data, tenantId) {
        const newPaimInstance = {
            id: (0, uuid_1.v4)(),
            ...data,
            status: paim_types_1.PaimInstanceStatus.Active,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
        };
        await (0, db_1.default)(this.tableName).insert({
            paim_instance_id: newPaimInstance.id,
            name: newPaimInstance.name,
            description: newPaimInstance.description,
            owner_id: newPaimInstance.ownerId,
            tier_name: newPaimInstance.tier,
            status: newPaimInstance.status,
            tenant_id: tenantId,
            created_at: newPaimInstance.createdAt,
            updated_at: newPaimInstance.updatedAt,
        });
        await this.auditTrailService.logEvent({
            tenantId,
            userId: newPaimInstance.ownerId,
            category: audit_types_1.AuditEventCategory.PAIM_OPERATION,
            severity: audit_types_1.AuditEventSeverity.INFO,
            operationType: 'PAIM_INSTANCE_CREATED',
            description: `PAIM instance ${newPaimInstance.name} created with ID ${newPaimInstance.id}`,
            timestamp: new Date(),
            resourceId: newPaimInstance.id,
            metadata: { name: newPaimInstance.name, tier: newPaimInstance.tier },
        });
        return newPaimInstance;
    }
    async getAllPaimInstances(tenantId, filters, pagination) {
        let query = (0, db_1.default)(this.tableName).where({ tenant_id: tenantId });
        if (filters.status) {
            query = query.where({ status: filters.status });
        }
        const totalElements = await query.clone().count('* as count').first();
        const offset = (pagination.page - 1) * pagination.size;
        const [sortBy, sortDirection] = pagination.sort ? pagination.sort.split(',') : ['created_at', 'desc'];
        const finalSortBy = sortBy || 'created_at';
        const finalSortDirection = sortDirection || 'desc';
        const paimInstancesDB = await query
            .select('paim_instance_id as id', 'name', 'description', 'owner_id as ownerId', 'tier_name as tier', 'status', 'created_at as createdAt', 'updated_at as updatedAt')
            .offset(offset)
            .limit(pagination.size)
            .orderBy(finalSortBy, finalSortDirection);
        const paimInstances = paimInstancesDB.map(this.mapDbToPaimInstance);
        return { data: paimInstances, totalElements: parseInt(totalElements.count, 10) };
    }
    async getPaimInstanceById(id, tenantId) {
        const paimInstanceDB = await (0, db_1.default)(this.tableName)
            .where({ paim_instance_id: id, tenant_id: tenantId })
            .select('paim_instance_id as id', 'name', 'description', 'owner_id as ownerId', 'tier_name as tier', 'status', 'created_at as createdAt', 'updated_at as updatedAt')
            .first();
        if (!paimInstanceDB) {
            return undefined;
        }
        return this.mapDbToPaimInstance(paimInstanceDB);
    }
    async updatePaimInstance(id, tenantId, data) {
        const updateData = {
            updated_at: new Date().toISOString(),
        };
        if (data.name)
            updateData.name = data.name;
        if (data.description)
            updateData.description = data.description;
        if (data.status)
            updateData.status = data.status;
        const updatedRows = await (0, db_1.default)(this.tableName)
            .where({ paim_instance_id: id, tenant_id: tenantId })
            .update(updateData);
        if (updatedRows === 0) {
            return undefined;
        }
        const updatedPaimInstance = await this.getPaimInstanceById(id, tenantId);
        if (updatedPaimInstance) {
            await this.auditTrailService.logEvent({
                tenantId,
                userId: updatedPaimInstance.ownerId,
                category: audit_types_1.AuditEventCategory.PAIM_OPERATION,
                severity: audit_types_1.AuditEventSeverity.INFO,
                operationType: 'PAIM_INSTANCE_UPDATED',
                description: `PAIM instance ${updatedPaimInstance.name} (ID: ${updatedPaimInstance.id}) updated`,
                timestamp: new Date(),
                resourceId: updatedPaimInstance.id,
                metadata: { updates: data },
            });
        }
        return updatedPaimInstance;
    }
    async deletePaimInstance(id, tenantId) {
        const paimInstance = await this.getPaimInstanceById(id, tenantId);
        if (!paimInstance) {
            return false;
        }
        const deletedRows = await (0, db_1.default)(this.tableName)
            .where({ paim_instance_id: id, tenant_id: tenantId })
            .del();
        if (deletedRows > 0) {
            await this.auditTrailService.logEvent({
                tenantId,
                userId: paimInstance.ownerId,
                category: audit_types_1.AuditEventCategory.PAIM_OPERATION,
                severity: audit_types_1.AuditEventSeverity.INFO,
                operationType: 'PAIM_INSTANCE_DELETED',
                description: `PAIM instance ${paimInstance.name} (ID: ${paimInstance.id}) deleted`,
                timestamp: new Date(),
                resourceId: paimInstance.id,
                metadata: { name: paimInstance.name },
            });
            return true;
        }
        return false;
    }
    async getPaimTierByName(tierName) {
        return (0, db_1.default)(this.paimTiersTableName).where({ tier_name: tierName }).first();
    }
    async getPaimTierById(tierId) {
        return (0, db_1.default)(this.paimTiersTableName).where({ paim_tier_id: tierId }).first();
    }
    async getAllPaimTiers() {
        return (0, db_1.default)(this.paimTiersTableName).select('*').orderBy('hierarchy_level', 'asc');
    }
    async getPaimHierarchy(paimInstanceId, tenantId) {
        const paimInstance = await this.getPaimInstanceById(paimInstanceId, tenantId);
        if (!paimInstance) {
            return undefined;
        }
        return {
            id: paimInstance.id,
            name: paimInstance.name,
            children: [],
        };
    }
    async updatePaimHierarchy(paimInstanceId, tenantId, hierarchyTree) {
        await this.auditTrailService.logEvent({
            tenantId,
            userId: 'SYSTEM',
            category: audit_types_1.AuditEventCategory.PAIM_OPERATION,
            severity: audit_types_1.AuditEventSeverity.INFO,
            operationType: 'PAIM_HIERARCHY_UPDATED',
            description: `PAIM hierarchy for instance ${paimInstanceId} updated`,
            timestamp: new Date(),
            resourceId: paimInstanceId,
            metadata: { hierarchyTree },
        });
        return true;
    }
    mapDbToPaimInstance(dbRow) {
        return {
            id: dbRow.id,
            name: dbRow.name,
            description: dbRow.description,
            ownerId: dbRow.ownerId,
            tier: dbRow.tier,
            status: dbRow.status,
            createdAt: dbRow.createdAt,
            updatedAt: dbRow.updatedAt,
        };
    }
}
exports.PaimRepository = PaimRepository;
