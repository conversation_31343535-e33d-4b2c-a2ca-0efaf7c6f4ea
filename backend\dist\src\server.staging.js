"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const express_rate_limit_1 = __importDefault(require("express-rate-limit"));
const dotenv_1 = __importDefault(require("dotenv"));
const logger_1 = __importDefault(require("./config/logger"));
const db_1 = __importDefault(require("./database/db"));
const error_handler_1 = require("./middleware/error-handler");
const monitoring_service_1 = require("./monitoring/monitoring.service");
const websocket_service_1 = require("./websocket/websocket.service");
const collaboration_events_1 = require("./websocket/events/collaboration.events");
const notification_service_1 = require("./notifications/notification.service");
const notification_repository_1 = require("./notifications/notification.repository");
const collaboration_session_service_1 = require("./collaboration/collaboration-session.service");
const paim_controller_1 = require("./paim/paim.controller");
const powerops_controller_1 = require("./powerops/powerops.controller");
const workflow_collaboration_controller_1 = require("./workflow-collaboration/workflow-collaboration.controller");
const workflow_collaboration_service_1 = require("./workflow-collaboration/workflow-collaboration.service");
const workflow_collaboration_repository_1 = require("./workflow-collaboration/workflow-collaboration.repository");
dotenv_1.default.config();
const app = (0, express_1.default)();
const PORT = process.env.PORT || 3001;
app.use((0, helmet_1.default)({
    contentSecurityPolicy: {
        directives: {
            defaultSrc: ["'self'"],
            scriptSrc: ["'self'", "'unsafe-inline'"],
            styleSrc: ["'self'", "'unsafe-inline'"],
            imgSrc: ["'self'", "data:", "https:"],
            connectSrc: ["'self'", "https://*.supabase.co"],
        },
    },
    crossOriginEmbedderPolicy: false,
}));
app.use((0, cors_1.default)({
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true
}));
const apiLimiter = (0, express_rate_limit_1.default)({
    windowMs: 15 * 60 * 1000,
    max: 100,
    message: 'Too many requests from this IP, please try again after 15 minutes',
    handler: (req, res, next) => {
        logger_1.default.warn(`Rate limit exceeded for IP: ${req.ip}`);
        res.status(429).json({
            status: 'error',
            code: 'TOO_MANY_REQUESTS',
            message: 'Too many requests, please try again later.',
        });
    },
});
app.use('/api/', apiLimiter);
app.use(express_1.default.json({ limit: '10mb' }));
app.use(express_1.default.urlencoded({ extended: true, limit: '10mb' }));
app.use((req, res, next) => {
    monitoringService.incrementMetric('api_requests_total');
    next();
});
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'theaigency-backend'
    });
});
app.get('/health/db', async (req, res) => {
    try {
        monitoringService.incrementMetric('db_queries_total');
        await db_1.default.raw('SELECT 1');
        res.status(200).json({
            status: 'healthy',
            database: 'connected',
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        monitoringService.incrementMetric('db_query_errors_total');
        logger_1.default.error('Database health check failed:', error);
        res.status(503).json({
            status: 'unhealthy',
            database: 'disconnected',
            timestamp: new Date().toISOString()
        });
    }
});
app.get('/health/metrics', (req, res) => {
    res.status(200).json(monitoringService.getMetrics());
});
app.get('/api', (req, res) => {
    res.json({
        name: 'TheAIgency Backend API',
        version: '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        timestamp: new Date().toISOString()
    });
});
let wsService;
let collaborationEvents;
let notificationService;
let collaborationSessionService;
let monitoringService;
const notificationRepository = new notification_repository_1.NotificationRepository();
const workflowCollaborationRepository = new workflow_collaboration_repository_1.WorkflowCollaborationRepository();
let workflowCollaborationService;
let workflowCollaborationController;
let powerOpsController;
app.use('*', (req, res) => {
    res.status(404).json({
        error: 'Route not found',
        path: req.originalUrl,
        method: req.method
    });
});
app.use((err, req, res, next) => {
    monitoringService.incrementMetric('api_errors_total');
    (0, error_handler_1.errorHandler)(err, req, res, next);
});
const server = app.listen(PORT, () => {
    logger_1.default.info(`🚀 Server running on port ${PORT}`);
    logger_1.default.info(`📊 Health check: http://localhost:${PORT}/health`);
    logger_1.default.info(`🗄️  Database health: http://localhost:${PORT}/health/db`);
    wsService = new websocket_service_1.WebSocketService(server);
    logger_1.default.info('WebSocket server initialized and attached to HTTP server.');
    collaborationEvents = new collaboration_events_1.CollaborationEvents(wsService);
    notificationService = new notification_service_1.NotificationService(wsService, notificationRepository);
    collaborationSessionService = new collaboration_session_service_1.CollaborationSessionService(wsService, collaborationEvents);
    logger_1.default.info('Collaboration Session Service initialized.');
    monitoringService = new monitoring_service_1.MonitoringService(wsService);
    logger_1.default.info('Monitoring Service initialized.');
    workflowCollaborationService = new workflow_collaboration_service_1.WorkflowCollaborationService(workflowCollaborationRepository, collaborationEvents, collaborationSessionService);
    workflowCollaborationController = new workflow_collaboration_controller_1.WorkflowCollaborationController(workflowCollaborationService);
    powerOpsController = new powerops_controller_1.PowerOpsController(notificationService);
    logger_1.default.info('PowerOps Controller initialized.');
    app.use('/api/v1/workflow-collaboration', workflowCollaborationController.router);
    logger_1.default.info('Registered /api/v1/workflow-collaboration routes');
    paimController = new paim_controller_1.PaimController(notificationService, wsService);
    logger_1.default.info('PAIM Controller initialized.');
    app.use('/api/v1/paim-instances', paimController.router);
    logger_1.default.info('Registered /api/v1/paim-instances routes');
    app.use('/api/v1/powerops', powerOpsController.router);
    logger_1.default.info('Registered /api/v1/powerops routes');
});
process.on('SIGTERM', () => {
    logger_1.default.info('SIGTERM received, shutting down gracefully');
    server.close(() => {
        logger_1.default.info('Process terminated');
        process.exit(0);
    });
});
process.on('SIGINT', () => {
    logger_1.default.info('SIGINT received, shutting down gracefully');
    server.close(() => {
        logger_1.default.info('Process terminated');
        process.exit(0);
    });
});
exports.default = app;
