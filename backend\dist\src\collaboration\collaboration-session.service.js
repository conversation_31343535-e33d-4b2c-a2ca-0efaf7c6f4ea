"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CollaborationSessionService = void 0;
class CollaborationSessionService {
    constructor(wsService, collaborationEvents) {
        this.wsService = wsService;
        this.collaborationEvents = collaborationEvents;
        this.activeSessions = new Map();
        this.sessionParticipants = new Map();
        this.workflowLocks = new Map();
    }
    async startSession(sessionData) {
        const newSession = {
            id: `session-${Date.now()}`,
            createdAt: new Date().toISOString(),
            status: 'active',
            participants: [],
            name: sessionData.name,
            ownerId: sessionData.ownerId,
            paimInstanceId: sessionData.paimInstanceId,
            description: sessionData.description,
            workflowId: sessionData.workflowId,
            sessionType: sessionData.sessionType,
        };
        this.activeSessions.set(newSession.id, newSession);
        this.sessionParticipants.set(newSession.id, new Set());
        if (sessionData.initialParticipants) {
            sessionData.initialParticipants.forEach(userId => this.joinSession(newSession.id, userId));
        }
        console.log(`Collaboration session ${newSession.id} started for workflow ${newSession.workflowId}`);
        return newSession;
    }
    async joinSession(sessionId, userId) {
        const session = this.activeSessions.get(sessionId);
        if (!session) {
            throw new Error('Collaboration session not found');
        }
        if (!this.sessionParticipants.has(sessionId)) {
            this.sessionParticipants.set(sessionId, new Set());
        }
        this.sessionParticipants.get(sessionId)?.add(userId);
        if (!session.participants.some(p => p.userId === userId)) {
            session.participants.push({ userId, joinedAt: new Date().toISOString() });
        }
        this.collaborationEvents.emitPresenceUpdate({
            workflowId: session.workflowId || '',
            userId,
            status: 'online',
        });
        console.log(`User ${userId} joined session ${sessionId}`);
        return session;
    }
    async leaveSession(sessionId, userId) {
        const session = this.activeSessions.get(sessionId);
        if (!session) {
            console.warn(`Attempted to leave non-existent session ${sessionId}`);
            return;
        }
        this.sessionParticipants.get(sessionId)?.delete(userId);
        session.participants = session.participants.filter(p => p.userId !== userId);
        this.collaborationEvents.emitPresenceUpdate({
            workflowId: session.workflowId || '',
            userId,
            status: 'offline',
        });
        console.log(`User ${userId} left session ${sessionId}`);
        if (this.sessionParticipants.get(sessionId)?.size === 0) {
            this.endSession(sessionId);
        }
    }
    async endSession(sessionId) {
        const session = this.activeSessions.get(sessionId);
        if (session) {
            session.status = 'ended';
            session.endedAt = new Date().toISOString();
            this.activeSessions.delete(sessionId);
            this.sessionParticipants.delete(sessionId);
            console.log(`Collaboration session ${sessionId} ended.`);
        }
    }
    getSessionParticipants(sessionId) {
        return Array.from(this.sessionParticipants.get(sessionId) || []);
    }
    handleCursorUpdate(payload) {
        this.collaborationEvents.emitCursorUpdate(payload);
    }
    handleEditUpdate(payload) {
        this.collaborationEvents.emitEditUpdate(payload);
    }
    async synchronizeWorkflowState(workflowId, newState) {
        this.collaborationEvents.emitWorkflowUpdate({
            workflowId: workflowId,
        });
        console.log(`Synchronizing workflow ${workflowId} state.`);
    }
    resolveConflict(workflowId, field, oldContent, newContent, userId) {
        console.log(`Resolving conflict for workflow ${workflowId}, field ${field} by user ${userId}`);
        return newContent;
    }
    acquireLock(workflowId, userId) {
        if (this.workflowLocks.has(workflowId) && this.workflowLocks.get(workflowId) !== userId) {
            console.log(`Workflow ${workflowId} is already locked by ${this.workflowLocks.get(workflowId)}`);
            return false;
        }
        this.workflowLocks.set(workflowId, userId);
        console.log(`User ${userId} acquired lock for workflow ${workflowId}`);
        return true;
    }
    releaseLock(workflowId, userId) {
        if (this.workflowLocks.get(workflowId) === userId) {
            this.workflowLocks.delete(workflowId);
            console.log(`User ${userId} released lock for workflow ${workflowId}`);
            return true;
        }
        console.log(`User ${userId} does not hold lock for workflow ${workflowId}`);
        return false;
    }
}
exports.CollaborationSessionService = CollaborationSessionService;
