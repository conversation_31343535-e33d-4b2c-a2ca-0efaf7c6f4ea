"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.exampleSchema = exports.validate = void 0;
const joi_1 = __importDefault(require("joi"));
const errors_1 = require("../utils/errors");
const logger_1 = __importDefault(require("../config/logger"));
const validate = (schema) => (req, res, next) => {
    const { error, value } = schema.validate(req.body, { abortEarly: false, allowUnknown: true });
    if (error) {
        const errors = error.details.map(detail => ({
            field: detail.path.join('.'),
            message: detail.message,
        }));
        logger_1.default.warn('Validation Error:', { errors, path: req.path, method: req.method });
        return next(new errors_1.ValidationError('Validation failed', errors));
    }
    req.body = sanitizeInput(value);
    next();
};
exports.validate = validate;
const sanitizeInput = (input) => {
    if (typeof input === 'string') {
        return input.trim();
    }
    if (typeof input === 'object' && input !== null) {
        for (const key in input) {
            if (Object.prototype.hasOwnProperty.call(input, key)) {
                input[key] = sanitizeInput(input[key]);
            }
        }
    }
    return input;
};
exports.exampleSchema = joi_1.default.object({
    name: joi_1.default.string().min(3).max(100).required(),
    email: joi_1.default.string().email().required(),
    age: joi_1.default.number().integer().min(0).max(120),
});
