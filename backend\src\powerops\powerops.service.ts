import { Injectable } from '@nestjs/common';
import { PowerOpsRepository } from './powerops.repository';
import db from '../database/db'; // Import the Knex instance
import { JwtPayload } from '../auth/auth.types'; // Import JwtPayload
import { AuthorizationService } from '../auth/authorization.service'; // Import AuthorizationService
import { PERMISSIONS } from '../auth/permissions'; // Import PERMISSIONS
import { NotificationService } from '../notifications/notification.service'; // Import NotificationService
import { PowerOpsAchievementPayload } from '../websocket/websocket.types'; // Import for WebSocket event payload
import {
  PowerOpsUsage,
  LogPowerOpsUsageRequest,
  Xp,
  AwardXpRequest,
  XpEvent,
  Badge,
  AwardBadgeRequest,
  Achievement,
  GrantAchievementRequest,
  Streak,
  Budget,
  CreateBudgetRequest,
  UpdateBudgetRequest,
  Invoice,
  CreateInvoiceRequest,
  Payment,
  ProcessPaymentRequest,
  LeaderboardEntry,
  CostOptimizationRecommendation,
  ResourceUsageLimit,
  SetResourceUsageLimitRequest,
  Notification,
  CreateNotificationRequest,
  EntityType,
  CostCategory,
} from './powerops.types';
import { CustomError, AuthorizationError } from '../utils/errors'; // Import AuthorizationError

export class PowerOpsService {
  private powerOpsRepository: PowerOpsRepository;
  private authorizationService: AuthorizationService;
  private notificationService: NotificationService; // Declare notificationService

  constructor(
    powerOpsRepository?: PowerOpsRepository,
    authorizationService?: AuthorizationService,
    notificationService?: NotificationService // Inject NotificationService
  ) {
    this.powerOpsRepository = powerOpsRepository || new PowerOpsRepository(db);
    this.authorizationService = authorizationService || new AuthorizationService();
    this.notificationService = notificationService || new NotificationService(
      // Placeholder for wsService and notificationRepository, will be injected from server.staging.ts
      {} as any, {} as any
    );
  }

  async scaleServiceResources(serviceName: string, direction: 'up' | 'down'): Promise<void> {
    console.log(`Simulating scaling ${direction} for service: ${serviceName}`);
    // In a real scenario, this would interact with infrastructure APIs
    // For example, updating replica counts in Kubernetes, or adjusting VM sizes in a cloud
    // You might also log this action to the audit system here.
  }

  // PowerOps Usage & Cost Management
  async logUsage(user: JwtPayload, data: LogPowerOpsUsageRequest): Promise<PowerOpsUsage> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_CREATE);
    const usageEntry = await this.powerOpsRepository.logPowerOpsUsage(data);
    // In a real system, this would trigger budget checks and notifications
    return {
      entityId: usageEntry.entityId,
      entityType: usageEntry.entityType,
      totalUsageUnits: usageEntry.usageUnits,
      estimatedCost: usageEntry.estimatedCost,
    };
  }

  async getUsage(user: JwtPayload, entityId: string, entityType: EntityType, startDate?: string, endDate?: string): Promise<PowerOpsUsage[]> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_READ);
    // For resource-level access, we need to fetch the resource first
    // Assuming PowerOpsUsage is tied to a PAIM instance or user/organization
    // This is a placeholder for actual resource fetching logic
    let resourceOwnerId: string | undefined;
    let resourceOrganizationId: string | undefined;
    let resourceTeamId: string | undefined;

    // Example: If entityType is PAIM_INSTANCE, fetch PAIM instance details
    if (entityType === EntityType.PaimInstance) {
      // Placeholder: In a real scenario, you'd fetch the PAIM instance from its repository
      // const paimInstance = await this.paimRepository.getById(entityId);
      // if (!paimInstance) throw new CustomError('PAIM instance not found', { originalStatusCode: 404 });
      // resourceOwnerId = paimInstance.ownerId;
      // resourceOrganizationId = paimInstance.organizationId;
      // resourceTeamId = paimInstance.teamId;
      // For now, we'll assume the entityId is the resourceId for canAccessResource
      resourceOwnerId = entityId; // Placeholder
      resourceOrganizationId = user.organizationId; // Placeholder
      resourceTeamId = user.teamId; // Placeholder
    } else if (entityType === EntityType.User) {
      resourceOwnerId = entityId;
      resourceOrganizationId = user.organizationId; // Assuming user belongs to an org
      resourceTeamId = user.teamId; // Assuming user belongs to a team
    } else if (entityType === EntityType.Organization) {
      resourceOrganizationId = entityId;
      resourceOwnerId = user.userId; // Assuming the user is the owner of the organization
      resourceTeamId = user.teamId; // Assuming user belongs to a team
    } else if (entityType === EntityType.Team) {
      resourceTeamId = entityId;
      resourceOwnerId = user.userId; // Assuming the user is the owner of the team
      resourceOrganizationId = user.organizationId; // Assuming team belongs to an org
    }

    if (resourceOwnerId || resourceOrganizationId || resourceTeamId) {
      this.authorizationService.canAccessResource(user, {
        ownerId: resourceOwnerId,
        organizationId: resourceOrganizationId,
        teamId: resourceTeamId,
      }, 'read'); // Assuming 'read' action for getUsage
    } else {
      // If no specific resource context, ensure user has broad read access
      this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_READ_ALL);
    }

    const usageEntries = await this.powerOpsRepository.getPowerOpsUsage(entityId, entityType, startDate, endDate);
    // Aggregate usage for PowerOpsUsage type
    const aggregatedUsage: { [key: string]: PowerOpsUsage } = {};

    usageEntries.forEach(entry => {
      const key = `${entry.entityId}-${entry.entityType}`;
      if (!aggregatedUsage[key]) {
        aggregatedUsage[key] = {
          entityId: entry.entityId,
          entityType: entry.entityType,
          totalUsageUnits: 0,
          estimatedCost: 0,
        };
      }
      aggregatedUsage[key].totalUsageUnits += entry.usageUnits;
      aggregatedUsage[key].estimatedCost += entry.estimatedCost;
    });

    return Object.values(aggregatedUsage);
  }

  async getBudgets(user: JwtPayload, entityId: string, entityType: EntityType): Promise<Budget[]> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_VIEW);
    // Resource-level access check for budgets
    let resourceOwnerId: string | undefined;
    let resourceOrganizationId: string | undefined;
    let resourceTeamId: string | undefined;

    if (entityType === EntityType.PaimInstance) {
      resourceOwnerId = entityId; // Placeholder
      resourceOrganizationId = user.organizationId; // Placeholder
      resourceTeamId = user.teamId; // Placeholder
    } else if (entityType === EntityType.User) {
      resourceOwnerId = entityId;
      resourceOrganizationId = user.organizationId;
      resourceTeamId = user.teamId;
    } else if (entityType === EntityType.Organization) {
      resourceOrganizationId = entityId;
      resourceOwnerId = user.userId;
      resourceTeamId = user.teamId;
    } else if (entityType === EntityType.Team) {
      resourceTeamId = entityId;
      resourceOwnerId = user.userId;
      resourceOrganizationId = user.organizationId;
    }

    if (resourceOwnerId || resourceOrganizationId || resourceTeamId) {
      this.authorizationService.canAccessResource(user, {
        ownerId: resourceOwnerId,
        organizationId: resourceOrganizationId,
        teamId: resourceTeamId,
      }, 'read'); // Assuming 'read' action for getBudgets
    } else {
      this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_VIEW_ALL);
    }
    return this.powerOpsRepository.getBudgets(entityId, entityType);
  }

  async createBudget(user: JwtPayload, data: CreateBudgetRequest): Promise<Budget> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_MANAGE);
    // Resource-level access check for creating budgets
    this.authorizationService.canAccessResource(user, {
      ownerId: data.entityType === EntityType.User ? data.entityId : undefined,
      organizationId: data.entityType === EntityType.Organization ? data.entityId : user.organizationId,
      teamId: data.entityType === EntityType.Team ? data.entityId : user.teamId,
    }, 'manage'); // Changed from 'create' to 'manage' for createBudget
    return this.powerOpsRepository.createBudget(data);
  }

  async updateBudget(user: JwtPayload, budgetId: string, data: UpdateBudgetRequest): Promise<Budget> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_MANAGE);
    const existingBudget = await this.powerOpsRepository.getBudgetById(budgetId);
    if (!existingBudget) {
      throw new CustomError('Budget not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
    }
    this.authorizationService.canAccessResource(user, {
      ownerId: existingBudget.entityType === EntityType.User ? existingBudget.entityId : undefined,
      organizationId: existingBudget.entityType === EntityType.Organization ? existingBudget.entityId : user.organizationId,
      teamId: existingBudget.entityType === EntityType.Team ? existingBudget.entityId : user.teamId,
    }, 'update'); // Assuming 'update' action for updateBudget
    const updatedBudget = await this.powerOpsRepository.updateBudget(budgetId, data);
    if (!updatedBudget) {
      throw new CustomError('Budget not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
    }
    return updatedBudget;
  }

  async deleteBudget(user: JwtPayload, budgetId: string): Promise<boolean> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_MANAGE);
    const existingBudget = await this.powerOpsRepository.getBudgetById(budgetId);
    if (!existingBudget) {
      throw new CustomError('Budget not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
    }
    this.authorizationService.canAccessResource(user, {
      ownerId: existingBudget.entityType === EntityType.User ? existingBudget.entityId : undefined,
      organizationId: existingBudget.entityType === EntityType.Organization ? existingBudget.entityId : user.organizationId,
      teamId: existingBudget.entityType === EntityType.Team ? existingBudget.entityId : user.teamId,
    }, 'delete'); // Assuming 'delete' action for deleteBudget
    const deleted = await this.powerOpsRepository.deleteBudget(budgetId);
    if (!deleted) {
      throw new CustomError('Budget not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
    }
    return deleted;
  }

  async getCostOptimizationRecommendations(user: JwtPayload, entityId: string, entityType: EntityType): Promise<CostOptimizationRecommendation[]> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_VIEW);
    let resourceOwnerId: string | undefined;
    let resourceOrganizationId: string | undefined;
    let resourceTeamId: string | undefined;

    if (entityType === EntityType.PaimInstance) {
      resourceOwnerId = entityId; // Placeholder
      resourceOrganizationId = user.organizationId; // Placeholder
      resourceTeamId = user.teamId; // Placeholder
    } else if (entityType === EntityType.User) {
      resourceOwnerId = entityId;
      resourceOrganizationId = user.organizationId;
      resourceTeamId = user.teamId;
    } else if (entityType === EntityType.Organization) {
      resourceOrganizationId = entityId;
      resourceOwnerId = user.userId;
      resourceTeamId = user.teamId;
    } else if (entityType === EntityType.Team) {
      resourceTeamId = entityId;
      resourceOwnerId = user.userId;
      resourceOrganizationId = user.organizationId;
    }

    if (resourceOwnerId || resourceOrganizationId || resourceTeamId) {
      this.authorizationService.canAccessResource(user, {
        ownerId: resourceOwnerId,
        organizationId: resourceOrganizationId,
        teamId: resourceTeamId,
      }, 'read'); // Assuming 'read' action for getCostOptimizationRecommendations
    } else {
      this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_VIEW_ALL);
    }
    // In a real system, this would involve analyzing usage patterns and suggesting optimizations.
    // For now, it's a direct call to the repository.
    return this.powerOpsRepository.getCostOptimizationRecommendations(entityId, entityType);
  }

  async getResourceUsageLimits(user: JwtPayload, entityId: string, entityType: EntityType): Promise<ResourceUsageLimit[]> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_VIEW);
    let resourceOwnerId: string | undefined;
    let resourceOrganizationId: string | undefined;
    let resourceTeamId: string | undefined;

    if (entityType === EntityType.PaimInstance) {
      resourceOwnerId = entityId; // Placeholder
      resourceOrganizationId = user.organizationId; // Placeholder
      resourceTeamId = user.teamId; // Placeholder
    } else if (entityType === EntityType.User) {
      resourceOwnerId = entityId;
      resourceOrganizationId = user.organizationId;
      resourceTeamId = user.teamId;
    } else if (entityType === EntityType.Organization) {
      resourceOrganizationId = entityId;
      resourceOwnerId = user.userId;
      resourceTeamId = user.teamId;
    } else if (entityType === EntityType.Team) {
      resourceTeamId = entityId;
      resourceOwnerId = user.userId;
      resourceOrganizationId = user.organizationId;
    }

    if (resourceOwnerId || resourceOrganizationId || resourceTeamId) {
      this.authorizationService.canAccessResource(user, {
        ownerId: resourceOwnerId,
        organizationId: resourceOrganizationId,
        teamId: resourceTeamId,
      }, 'read'); // Assuming 'read' action for getResourceUsageLimits
    } else {
      this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_VIEW_ALL);
    }
    return this.powerOpsRepository.getResourceUsageLimits(entityId, entityType);
  }

  async setResourceUsageLimit(user: JwtPayload, data: SetResourceUsageLimitRequest): Promise<ResourceUsageLimit> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_MANAGE);
    this.authorizationService.canAccessResource(user, {
      ownerId: data.entityType === EntityType.User ? data.entityId : undefined,
      organizationId: data.entityType === EntityType.Organization ? data.entityId : user.organizationId,
      teamId: data.entityType === EntityType.Team ? data.entityId : user.teamId,
    }, 'manage'); // Assuming 'manage' action for setResourceUsageLimit
    return this.powerOpsRepository.setResourceUsageLimit(data);
  }

  // Gamification (XP, Badges, Achievements, Streaks)
  async getXp(user: JwtPayload, id: string, entityType: EntityType): Promise<Xp> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_READ);
    let resourceOwnerId: string | undefined;
    let resourceOrganizationId: string | undefined;
    let resourceTeamId: string | undefined;

    if (entityType === EntityType.PaimInstance) {
      resourceOwnerId = id; // Placeholder
      resourceOrganizationId = user.organizationId; // Placeholder
      resourceTeamId = user.teamId; // Placeholder
    } else if (entityType === EntityType.User) {
      resourceOwnerId = id;
      resourceOrganizationId = user.organizationId;
      resourceTeamId = user.teamId;
    } else if (entityType === EntityType.Organization) {
      resourceOrganizationId = id;
      resourceOwnerId = user.userId;
      resourceTeamId = user.teamId;
    } else if (entityType === EntityType.Team) {
      resourceTeamId = id;
      resourceOwnerId = user.userId;
      resourceOrganizationId = user.organizationId;
    }

    if (resourceOwnerId || resourceOrganizationId || resourceTeamId) {
      this.authorizationService.canAccessResource(user, {
        ownerId: resourceOwnerId,
        organizationId: resourceOrganizationId,
        teamId: resourceTeamId,
      }, 'read'); // Assuming 'read' action for getXp
    } else {
      this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_READ_ALL);
    }
    const xp = await this.powerOpsRepository.getXp(id, entityType);
    if (!xp) {
      // Return a default XP object if not found
      return { entityId: id, entityType, currentXp: 0, level: 1 };
    }
    return xp;
  }

  async awardXp(user: JwtPayload, data: AwardXpRequest): Promise<Xp> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_MANAGE_ALL);
    // The repository now handles XP calculation and level progression.
    // We directly pass the AwardXpRequest to the repository.
    this.authorizationService.canAccessResource(user, {
      ownerId: data.entityType === EntityType.User ? data.entityId : undefined,
      organizationId: data.entityType === EntityType.Organization ? data.entityId : user.organizationId,
      teamId: data.entityType === EntityType.Team ? data.entityId : user.teamId,
    }, 'manage');
    const updatedXp = await this.powerOpsRepository.awardXp(data);

    // Check for level-up and send notification
    if (updatedXp.level > (await this.powerOpsRepository.getXp(data.entityId, data.entityType))?.level || 0) {
      await this.notificationService.createNotification({
        userId: data.entityId, // Assuming entityId is userId for notifications
        type: 'success',
        message: `Congratulations! You've reached PowerOps Level ${updatedXp.level}!`,
      });
      // Emit WebSocket event for level-up
      // Note: PowerOpsAchievementPayload is a generic name, could be more specific like PowerOpsLevelUpPayload
      this.notificationService.wsService.broadcast(JSON.stringify({
        event: 'powerOpsAchievement', // Using the generic event for now
        payload: {
          userId: data.entityId,
          achievementName: `Level Up: ${updatedXp.level}`,
          levelUp: true,
        } as PowerOpsAchievementPayload,
      }));
    }
    return updatedXp;
  }

  async getAllBadges(user: JwtPayload): Promise<Badge[]> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_READ);
    return this.powerOpsRepository.getAllBadges();
  }

  async getBadges(user: JwtPayload, entityId: string, entityType: EntityType): Promise<Badge[]> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_READ);
    let resourceOwnerId: string | undefined;
    let resourceOrganizationId: string | undefined;
    let resourceTeamId: string | undefined;

    if (entityType === EntityType.PaimInstance) {
      resourceOwnerId = entityId; // Placeholder
      resourceOrganizationId = user.organizationId; // Placeholder
      resourceTeamId = user.teamId; // Placeholder
    } else if (entityType === EntityType.User) {
      resourceOwnerId = entityId;
      resourceOrganizationId = user.organizationId;
      resourceTeamId = user.teamId;
    } else if (entityType === EntityType.Organization) {
      resourceOrganizationId = entityId;
      resourceOwnerId = user.userId;
      resourceTeamId = user.teamId;
    } else if (entityType === EntityType.Team) {
      resourceTeamId = entityId;
      resourceOwnerId = user.userId;
      resourceOrganizationId = user.organizationId;
    }

    if (resourceOwnerId || resourceOrganizationId || resourceTeamId) {
      this.authorizationService.canAccessResource(user, {
        ownerId: resourceOwnerId,
        organizationId: resourceOrganizationId,
        teamId: resourceTeamId,
      }, 'read'); // Assuming 'read' action for getBadges
    } else {
      this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_READ_ALL);
    }
    return this.powerOpsRepository.getBadgesForEntity(entityId, entityType);
  }

  async awardBadge(user: JwtPayload, data: AwardBadgeRequest): Promise<Badge> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_MANAGE_ALL);
    this.authorizationService.canAccessResource(user, {
      ownerId: data.entityType === EntityType.User ? data.entityId : undefined,
      organizationId: data.entityType === EntityType.Organization ? data.entityId : user.organizationId,
      teamId: data.entityType === EntityType.Team ? data.entityId : user.teamId,
    }, 'manage'); // Assuming 'manage' action for awardBadge
    // Logic to check if badge can be awarded (e.g., prerequisites)
    // For now, directly award
    return this.powerOpsRepository.awardBadge(data);
  }

  async evaluateBadgeEligibility(user: JwtPayload): Promise<void> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_MANAGE_ALL);
    console.log('Evaluating badge eligibility for all users/organizations (MVP placeholder)...');
    // For MVP, this method remains a placeholder.
    // In a full implementation, this would be a scheduled job that:
    // 1. Fetches all entities (users/PAIM instances).
    // 2. Retrieves their current XP and other relevant metrics.
    // 3. Iterates through defined badge unlock rules.
    // 4. Awards badges using this.awardBadge if criteria are met.
    // The complexity of badge rules and their evaluation is significant and deferred for later phases.
  }

  async getAchievements(user: JwtPayload, entityId: string, entityType: EntityType): Promise<Achievement[]> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_READ);
    let resourceOwnerId: string | undefined;
    let resourceOrganizationId: string | undefined;
    let resourceTeamId: string | undefined;

    if (entityType === EntityType.PaimInstance) {
      resourceOwnerId = entityId; // Placeholder
      resourceOrganizationId = user.organizationId; // Placeholder
      resourceTeamId = user.teamId; // Placeholder
    } else if (entityType === EntityType.User) {
      resourceOwnerId = entityId;
      resourceOrganizationId = user.organizationId;
      resourceTeamId = user.teamId;
    } else if (entityType === EntityType.Organization) {
      resourceOrganizationId = entityId;
      resourceOwnerId = user.userId;
      resourceTeamId = user.teamId;
    } else if (entityType === EntityType.Team) {
      resourceTeamId = entityId;
      resourceOwnerId = user.userId;
      resourceOrganizationId = user.organizationId;
    }

    if (resourceOwnerId || resourceOrganizationId || resourceTeamId) {
      this.authorizationService.canAccessResource(user, {
        ownerId: resourceOwnerId,
        organizationId: resourceOrganizationId,
        teamId: resourceTeamId,
      }, 'read'); // Assuming 'read' action for getAchievements
    } else {
      this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_READ_ALL);
    }
    return this.powerOpsRepository.getAchievements(entityId, entityType);
  }

  async grantAchievement(user: JwtPayload, data: GrantAchievementRequest): Promise<Achievement> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_MANAGE_ALL);
    this.authorizationService.canAccessResource(user, {
      ownerId: data.entityType === EntityType.User ? data.entityId : undefined,
      organizationId: data.entityType === EntityType.Organization ? data.entityId : user.organizationId,
      teamId: data.entityType === EntityType.Team ? data.entityId : user.teamId,
    }, 'manage');
    const newAchievement = await this.powerOpsRepository.grantAchievement(data);

    // Send notification for achievement
    await this.notificationService.createNotification({
      userId: data.entityId, // Assuming entityId is userId for notifications
      type: 'success',
      message: `Achievement Unlocked: ${newAchievement.name}!`,
    });
    // Emit WebSocket event for achievement
    this.notificationService.wsService.broadcast(JSON.stringify({
      event: 'powerOpsAchievement',
      payload: {
        userId: data.entityId,
        achievementName: newAchievement.name,
        levelUp: false,
      } as PowerOpsAchievementPayload,
    }));
    return newAchievement;
  }

  async getStreaks(user: JwtPayload, entityId: string, entityType: EntityType): Promise<Streak[]> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_READ);
    let resourceOwnerId: string | undefined;
    let resourceOrganizationId: string | undefined;
    let resourceTeamId: string | undefined;

    if (entityType === EntityType.PaimInstance) {
      resourceOwnerId = entityId; // Placeholder
      resourceOrganizationId = user.organizationId; // Placeholder
      resourceTeamId = user.teamId; // Placeholder
    } else if (entityType === EntityType.User) {
      resourceOwnerId = entityId;
      resourceOrganizationId = user.organizationId;
      resourceTeamId = user.teamId;
    } else if (entityType === EntityType.Organization) {
      resourceOrganizationId = entityId;
      resourceOwnerId = user.userId;
      resourceTeamId = user.teamId;
    } else if (entityType === EntityType.Team) {
      resourceTeamId = entityId;
      resourceOwnerId = user.userId;
      resourceOrganizationId = user.organizationId;
    }

    if (resourceOwnerId || resourceOrganizationId || resourceTeamId) {
      this.authorizationService.canAccessResource(user, {
        ownerId: resourceOwnerId,
        organizationId: resourceOrganizationId,
        teamId: resourceTeamId,
      }, 'read'); // Assuming 'read' action for getStreaks
    } else {
      this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_READ_ALL);
    }
    return this.powerOpsRepository.getStreaks(entityId, entityType);
  }

  async getLeaderboard(user: JwtPayload, metric: string, limit?: number): Promise<LeaderboardEntry[]> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_READ);
    // This would involve more complex logic to calculate and sort leaderboard entries
    return this.powerOpsRepository.getLeaderboard(metric, limit);
  }

  // Billing and Payments
  async getInvoices(user: JwtPayload, entityId: string, entityType: EntityType): Promise<Invoice[]> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_VIEW);
    let resourceOwnerId: string | undefined;
    let resourceOrganizationId: string | undefined;
    let resourceTeamId: string | undefined;

    if (entityType === EntityType.PaimInstance) {
      resourceOwnerId = entityId; // Placeholder
      resourceOrganizationId = user.organizationId; // Placeholder
      resourceTeamId = user.teamId; // Placeholder
    } else if (entityType === EntityType.User) {
      resourceOwnerId = entityId;
      resourceOrganizationId = user.organizationId;
      resourceTeamId = user.teamId;
    } else if (entityType === EntityType.Organization) {
      resourceOrganizationId = entityId;
      resourceOwnerId = user.userId;
      resourceTeamId = user.teamId;
    } else if (entityType === EntityType.Team) {
      resourceTeamId = entityId;
      resourceOwnerId = user.userId;
      resourceOrganizationId = user.organizationId;
    }

    if (resourceOwnerId || resourceOrganizationId || resourceTeamId) {
      this.authorizationService.canAccessResource(user, {
        ownerId: resourceOwnerId,
        organizationId: resourceOrganizationId,
        teamId: resourceTeamId,
      }, 'read'); // Assuming 'read' action for getInvoices
    } else {
      this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_VIEW_ALL);
    }
    return this.powerOpsRepository.getInvoices(entityId, entityType);
  }

  async createInvoice(user: JwtPayload, data: CreateInvoiceRequest): Promise<Invoice> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_MANAGE);
    this.authorizationService.canAccessResource(user, {
      ownerId: data.entityType === EntityType.User ? data.entityId : undefined,
      organizationId: data.entityType === EntityType.Organization ? data.entityId : user.organizationId,
      teamId: data.entityType === EntityType.Team ? data.entityId : user.teamId,
    }, 'manage'); // Changed from 'create' to 'manage' for createInvoice
    // Logic for complex invoice generation, e.g., pulling usage data for line items
    return this.powerOpsRepository.createInvoice(data);
  }

  async processPayment(user: JwtPayload, data: ProcessPaymentRequest): Promise<Payment> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.BILLING_MANAGE);
    this.authorizationService.canAccessResource(user, {
      ownerId: data.entityType === EntityType.User ? data.entityId : undefined,
      organizationId: data.entityType === EntityType.Organization ? data.entityId : user.organizationId,
      teamId: data.entityType === EntityType.Team ? data.entityId : user.teamId,
    }, 'manage'); // Assuming 'manage' action for processPayment
    // Integration with external payment systems would happen here
    // For now, it's a direct call to the repository
    return this.powerOpsRepository.processPayment(data);
  }

  // Notifications
  async getNotifications(user: JwtPayload, entityId: string, entityType: EntityType): Promise<Notification[]> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_READ);
    let resourceOwnerId: string | undefined;
    let resourceOrganizationId: string | undefined;
    let resourceTeamId: string | undefined;

    if (entityType === EntityType.PaimInstance) {
      resourceOwnerId = entityId; // Placeholder
      resourceOrganizationId = user.organizationId; // Placeholder
      resourceTeamId = user.teamId; // Placeholder
    } else if (entityType === EntityType.User) {
      resourceOwnerId = entityId;
      resourceOrganizationId = user.organizationId;
      resourceTeamId = user.teamId;
    } else if (entityType === EntityType.Organization) {
      resourceOrganizationId = entityId;
      resourceOwnerId = user.userId;
      resourceTeamId = user.teamId;
    } else if (entityType === EntityType.Team) {
      resourceTeamId = entityId;
      resourceOwnerId = user.userId;
      resourceOrganizationId = user.organizationId;
    }

    if (resourceOwnerId || resourceOrganizationId || resourceTeamId) {
      this.authorizationService.canAccessResource(user, {
        ownerId: resourceOwnerId,
        organizationId: resourceOrganizationId,
        teamId: resourceTeamId,
      }, 'read'); // Assuming 'read' action for getNotifications
    } else {
      this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_READ_ALL);
    }
    return this.powerOpsRepository.getNotifications(entityId, entityType);
  }

  async createNotification(user: JwtPayload, data: CreateNotificationRequest): Promise<Notification> {
    this.authorizationService.hasPermission(user.roles, user.paimTier, PERMISSIONS.POWER_OPS_CREATE);
    this.authorizationService.canAccessResource(user, {
      ownerId: data.entityType === EntityType.User ? data.entityId : undefined,
      organizationId: data.entityType === EntityType.Organization ? data.entityId : user.organizationId,
      teamId: data.entityType === EntityType.Team ? data.entityId : user.teamId,
    }, 'manage'); // Changed from 'create' to 'manage' for createNotification
    return this.powerOpsRepository.createNotification(data);
  }

  // Helper for calculating usage cost (can be moved to a utility or pricing service)
  private calculateUsageCost(usageUnits: number, costCategory: CostCategory): number {
    // This is a placeholder. In a real system, this would come from a configuration or pricing service.
    switch (costCategory) {
      case CostCategory.Compute: return usageUnits * 0.01;
      case CostCategory.Storage: return usageUnits * 0.001;
      case CostCategory.Bandwidth: return usageUnits * 0.005;
      case CostCategory.AIModel: return usageUnits * 0.05;
      default: return usageUnits * 0.01;
    }
  }
}