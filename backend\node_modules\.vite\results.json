{"version": "3.1.4", "results": [[":src/utils/http-client.service.test.ts", {"duration": 0, "failed": true}], [":src/organization/organization.service.test.ts", {"duration": 35.622499999999945, "failed": true}], [":src/utils/http-client.validation.test.ts", {"duration": 70103.4006, "failed": true}], [":src/organization/organization.controller.test.ts", {"duration": 0, "failed": true}], [":src/powerops/powerops.repository.test.ts", {"duration": 23.701500000000124, "failed": true}], [":src/database/adapters/appwrite-adapter.test.ts", {"duration": 0, "failed": true}], [":src/powerops/powerops.controller.test.ts", {"duration": 0, "failed": true}], [":src/creative-api/creative-api.service.test.ts", {"duration": 1408.0092, "failed": false}], [":src/organization/organization.repository.test.ts", {"duration": 13.200199999999995, "failed": false}], [":src/auth/auth.service.test.ts", {"duration": 29.902799999999957, "failed": true}], [":src/billing/billing.repository.test.ts", {"duration": 17.613899999999944, "failed": true}], [":src/powerops/powerops.service.test.ts", {"duration": 19.37980000000016, "failed": true}], [":src/billing/billing.service.test.ts", {"duration": 13.274400000000014, "failed": true}], [":src/monitoring/monitoring.controller.test.ts", {"duration": 14.290199999999913, "failed": true}], [":src/billing/billing.integration.test.ts", {"duration": 0, "failed": true}]]}