"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = void 0;
exports.config = {
    openaiApiKey: '',
    runwaymlApiKey: '',
    adobeFireflyApiKey: '',
    database: {
        host: 'localhost',
        port: 5432,
        user: 'postgres',
        password: '',
        name: 'theaigency_db'
    },
    jwt: {
        secret: 'your_super_secret_jwt_key_here_12345',
        accessTokenExpiration: '15m',
        refreshTokenExpiration: '7d'
    },
    server: {
        port: 3000,
        nodeEnv: 'development'
    },
    redis: {
        url: 'redis://localhost:6379'
    },
    qdrant: {
        url: 'http://localhost:6333'
    }
};
exports.default = exports.config;
