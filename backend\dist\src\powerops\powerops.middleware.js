"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PowerOpsMiddleware = void 0;
const powerops_service_1 = require("./powerops.service");
const powerops_types_1 = require("./powerops.types");
class PowerOpsMiddleware {
    constructor() {
        this.logApiUsage = async (req, res, next) => {
            const startTime = process.hrtime.bigint();
            res.on('finish', async () => {
                const endTime = process.hrtime.bigint();
                const durationMs = Number(endTime - startTime) / 1000000;
                const entityId = req.user?.userId || 'anonymous';
                const entityType = req.user?.userId ? powerops_types_1.EntityType.User : powerops_types_1.EntityType.PaimInstance;
                const requestSize = req.socket.bytesRead || 0;
                const responseSize = res.socket?.bytesWritten || 0;
                const usageUnits = (requestSize + responseSize) / 1024 + (durationMs / 100);
                try {
                    await this.powerOpsService.logUsage({
                        entityId,
                        entityType,
                        usageUnits,
                        costCategory: powerops_types_1.CostCategory.Compute,
                        description: `API Call: ${req.method} ${req.originalUrl}`,
                        timestamp: new Date().toISOString(),
                    });
                }
                catch (error) {
                    console.error('Error logging PowerOps usage:', error);
                }
            });
            next();
        };
        this.powerOpsService = new powerops_service_1.PowerOpsService();
    }
}
exports.PowerOpsMiddleware = PowerOpsMiddleware;
