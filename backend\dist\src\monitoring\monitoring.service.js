"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MonitoringService = void 0;
const logger_1 = __importDefault(require("../config/logger"));
const websocket_types_1 = require("../websocket/websocket.types");
class MonitoringService {
    constructor(wsService) {
        this.wsService = wsService;
        this.metrics = {
            api_requests_total: 0,
            api_errors_total: 0,
            db_queries_total: 0,
            db_query_errors_total: 0,
        };
    }
    incrementMetric(metricName, value = 1) {
        if (this.metrics.hasOwnProperty(metricName)) {
            this.metrics[metricName] += value;
            this.emitDashboardUpdate();
        }
        else {
            logger_1.default.warn(`Attempted to increment unknown metric: ${metricName}`);
        }
    }
    getMetrics() {
        return { ...this.metrics };
    }
    resetMetrics() {
        for (const key in this.metrics) {
            if (this.metrics.hasOwnProperty(key)) {
                this.metrics[key] = 0;
            }
        }
        logger_1.default.info('Metrics reset.');
        this.emitDashboardUpdate();
    }
    emitDashboardUpdate() {
        const payload = {
            metrics: this.getMetrics(),
        };
        this.wsService.broadcast(JSON.stringify({
            event: websocket_types_1.WebSocketEvent.DASHBOARD_UPDATE,
            payload,
        }));
    }
}
exports.MonitoringService = MonitoringService;
