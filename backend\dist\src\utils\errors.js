"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = exports.ConflictError = exports.ServiceUnavailableError = exports.CustomError = exports.ExternalServiceError = exports.DatabaseError = exports.NotFoundError = exports.AuthorizationError = exports.AuthenticationError = exports.ValidationError = exports.BaseError = void 0;
class BaseError extends Error {
    constructor(message, details) {
        super(message);
        this.details = details;
        this.name = this.constructor.name;
        Error.captureStackTrace(this, this.constructor);
    }
}
exports.BaseError = BaseError;
exports.default = BaseError;
class ValidationError extends BaseError {
    constructor() {
        super(...arguments);
        this.statusCode = 400;
        this.isOperational = true;
        this.errorCode = 'VALIDATION_ERROR';
    }
}
exports.ValidationError = ValidationError;
class AuthenticationError extends BaseError {
    constructor() {
        super(...arguments);
        this.statusCode = 401;
        this.isOperational = true;
        this.errorCode = 'AUTHENTICATION_ERROR';
    }
}
exports.AuthenticationError = AuthenticationError;
class AuthorizationError extends BaseError {
    constructor() {
        super(...arguments);
        this.statusCode = 403;
        this.isOperational = true;
        this.errorCode = 'AUTHORIZATION_ERROR';
    }
}
exports.AuthorizationError = AuthorizationError;
class NotFoundError extends BaseError {
    constructor() {
        super(...arguments);
        this.statusCode = 404;
        this.isOperational = true;
        this.errorCode = 'NOT_FOUND_ERROR';
    }
}
exports.NotFoundError = NotFoundError;
class DatabaseError extends BaseError {
    constructor() {
        super(...arguments);
        this.statusCode = 500;
        this.isOperational = true;
        this.errorCode = 'DATABASE_ERROR';
    }
}
exports.DatabaseError = DatabaseError;
class ExternalServiceError extends BaseError {
    constructor() {
        super(...arguments);
        this.statusCode = 502;
        this.isOperational = true;
        this.errorCode = 'EXTERNAL_SERVICE_ERROR';
    }
}
exports.ExternalServiceError = ExternalServiceError;
class CustomError extends BaseError {
    constructor() {
        super(...arguments);
        this.statusCode = 500;
        this.isOperational = true;
        this.errorCode = 'CUSTOM_ERROR';
    }
}
exports.CustomError = CustomError;
class ServiceUnavailableError extends BaseError {
    constructor() {
        super(...arguments);
        this.statusCode = 503;
        this.isOperational = true;
        this.errorCode = 'SERVICE_UNAVAILABLE';
    }
}
exports.ServiceUnavailableError = ServiceUnavailableError;
class ConflictError extends BaseError {
    constructor() {
        super(...arguments);
        this.statusCode = 409;
        this.isOperational = true;
        this.errorCode = 'CONFLICT_ERROR';
    }
}
exports.ConflictError = ConflictError;
