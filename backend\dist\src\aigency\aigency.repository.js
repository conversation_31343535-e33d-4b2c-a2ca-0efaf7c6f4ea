"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AigencyRepository = void 0;
const db_1 = __importDefault(require("../database/db"));
const audit_types_1 = require("../audit/audit.types");
class AigencyRepository {
    constructor(auditTrailService) {
        this.auditTrailService = auditTrailService;
        this.knowledgeBasesTable = 'knowledge_bases';
        this.documentsTable = 'documents';
        this.documentChunksTable = 'document_chunks';
        this.auditTrailTable = 'ai_operations_audit_trail';
    }
    async createKnowledgeBase(tenantId, name, description) {
        const newKb = await db_1.default.raw(`INSERT INTO ${this.knowledgeBasesTable} (tenant_id, name, description) VALUES (?, ?, ?) RETURNING *`, [tenantId, name, description]).then(res => res.rows[0]);
        await this.auditTrailService.logEvent({
            tenantId,
            userId: 'system',
            category: audit_types_1.AuditEventCategory.AI_OPERATION,
            severity: audit_types_1.AuditEventSeverity.INFO,
            operationType: 'KNOWLEDGE_BASE_CREATED',
            description: `Knowledge Base '${name}' created with ID ${newKb.id}`,
            timestamp: new Date(),
            resourceId: newKb.id,
            metadata: { name: newKb.name },
        });
        return newKb;
    }
    async getKnowledgeBaseById(tenantId, id) {
        const kb = await db_1.default.raw(`SELECT * FROM ${this.knowledgeBasesTable} WHERE id = ? AND tenant_id = ?`, [id, tenantId]).then(res => res.rows[0] || undefined);
        return kb;
    }
    async getKnowledgeBaseByName(tenantId, name) {
        const kb = await db_1.default.raw(`SELECT * FROM ${this.knowledgeBasesTable} WHERE name = ? AND tenant_id = ?`, [name, tenantId]).then(res => res.rows[0] || undefined);
        return kb;
    }
    async listKnowledgeBases(tenantId) {
        const kbs = await db_1.default.raw(`SELECT * FROM ${this.knowledgeBasesTable} WHERE tenant_id = ?`, [tenantId]).then(res => res.rows);
        return kbs;
    }
    async updateKnowledgeBase(tenantId, id, updates) {
        const updatedKb = await db_1.default.raw(`UPDATE ${this.knowledgeBasesTable} SET name = COALESCE(?, name), description = COALESCE(?, description), updated_at = NOW() WHERE id = ? AND tenant_id = ? RETURNING *`, [updates.name, updates.description, id, tenantId]).then(res => res.rows[0] || undefined);
        if (updatedKb) {
            await this.auditTrailService.logEvent({
                tenantId,
                userId: 'system',
                category: audit_types_1.AuditEventCategory.AI_OPERATION,
                severity: audit_types_1.AuditEventSeverity.INFO,
                operationType: 'KNOWLEDGE_BASE_UPDATED',
                description: `Knowledge Base '${updatedKb.name}' (ID: ${updatedKb.id}) updated`,
                timestamp: new Date(),
                resourceId: updatedKb.id,
                metadata: { updates },
            });
        }
        return updatedKb;
    }
    async deleteKnowledgeBase(tenantId, id) {
        const deletedCount = await db_1.default.raw(`DELETE FROM ${this.knowledgeBasesTable} WHERE id = ? AND tenant_id = ? RETURNING id`, [id, tenantId]).then(res => res.rows.length);
        if (deletedCount > 0) {
            await this.auditTrailService.logEvent({
                tenantId,
                userId: 'system',
                category: audit_types_1.AuditEventCategory.AI_OPERATION,
                severity: audit_types_1.AuditEventSeverity.INFO,
                operationType: 'KNOWLEDGE_BASE_DELETED',
                description: `Knowledge Base with ID ${id} deleted`,
                timestamp: new Date(),
                resourceId: id,
            });
            return true;
        }
        return false;
    }
    async createDocument(knowledgeBaseId, tenantId, fileName, fileType, contentHash, chunkCount) {
        const newDoc = await db_1.default.raw(`INSERT INTO ${this.documentsTable} (knowledge_base_id, tenant_id, file_name, file_type, content_hash, chunk_count) VALUES (?, ?, ?, ?, ?, ?) RETURNING *`, [knowledgeBaseId, tenantId, fileName, fileType, contentHash, chunkCount]).then(res => res.rows[0]);
        await this.auditTrailService.logEvent({
            tenantId,
            userId: 'system',
            category: audit_types_1.AuditEventCategory.AI_OPERATION,
            severity: audit_types_1.AuditEventSeverity.INFO,
            operationType: 'DOCUMENT_UPLOADED',
            description: `Document '${fileName}' (ID: ${newDoc.id}) uploaded to KB ${knowledgeBaseId}`,
            timestamp: new Date(),
            resourceId: newDoc.id,
            metadata: { knowledgeBaseId, fileName },
        });
        return newDoc;
    }
    async getDocumentById(tenantId, id) {
        const doc = await db_1.default.raw(`SELECT * FROM ${this.documentsTable} WHERE id = ? AND tenant_id = ?`, [id, tenantId]).then(res => res.rows[0] || undefined);
        return doc;
    }
    async getDocumentByContentHash(tenantId, contentHash) {
        const doc = await db_1.default.raw(`SELECT * FROM ${this.documentsTable} WHERE content_hash = ? AND tenant_id = ?`, [contentHash, tenantId]).then(res => res.rows[0] || undefined);
        return doc;
    }
    async listDocumentsInKnowledgeBase(knowledgeBaseId, tenantId) {
        const docs = await db_1.default.raw(`SELECT * FROM ${this.documentsTable} WHERE knowledge_base_id = ? AND tenant_id = ?`, [knowledgeBaseId, tenantId]).then(res => res.rows);
        return docs;
    }
    async deleteDocument(tenantId, id) {
        const deletedCount = await db_1.default.raw(`DELETE FROM ${this.documentsTable} WHERE id = ? AND tenant_id = ? RETURNING id`, [id, tenantId]).then(res => res.rows.length);
        if (deletedCount > 0) {
            await this.auditTrailService.logEvent({
                tenantId,
                userId: 'system',
                category: audit_types_1.AuditEventCategory.AI_OPERATION,
                severity: audit_types_1.AuditEventSeverity.INFO,
                operationType: 'DOCUMENT_DELETED',
                description: `Document with ID ${id} deleted`,
                timestamp: new Date(),
                resourceId: id,
            });
            return true;
        }
        return false;
    }
    async createDocumentChunk(documentId, knowledgeBaseId, tenantId, content, embedding, chunkIndex) {
        const newChunk = await db_1.default.raw(`INSERT INTO ${this.documentChunksTable} (document_id, knowledge_base_id, tenant_id, content, embedding, chunk_index) VALUES (?, ?, ?, ?, ?, ?) RETURNING *`, [documentId, knowledgeBaseId, tenantId, content, `[${embedding.join(',')}]`, chunkIndex]).then(res => res.rows[0]);
        return newChunk;
    }
    async getDocumentChunkById(tenantId, id) {
        const chunk = await db_1.default.raw(`SELECT * FROM ${this.documentChunksTable} WHERE id = ? AND tenant_id = ?`, [id, tenantId]).then(res => res.rows[0] || undefined);
        return chunk;
    }
    async getDocumentChunksByDocumentId(tenantId, documentId) {
        const chunks = await db_1.default.raw(`SELECT * FROM ${this.documentChunksTable} WHERE document_id = ? AND tenant_id = ? ORDER BY chunk_index ASC`, [documentId, tenantId]).then(res => res.rows);
        return chunks;
    }
    async vectorSearch(tenantId, knowledgeBaseId, queryEmbedding, topK = 5) {
        const results = await db_1.default.raw(`SELECT
        id,
        document_id as "documentId",
        knowledge_base_id as "knowledgeBaseId",
        content,
        embedding <=> ? as similarity
      FROM ${this.documentChunksTable}
      WHERE knowledge_base_id = ? AND tenant_id = ?
      ORDER BY similarity ASC
      LIMIT ?`, [`[${queryEmbedding.join(',')}]`, knowledgeBaseId, tenantId, topK]).then(res => res.rows);
        return results.map(row => ({
            ...row,
            similarity: 1 - row.similarity,
        }));
    }
    async logAIOperation(log) {
        await this.auditTrailService.logEvent({
            tenantId: log.tenantId,
            userId: log.userId || 'system',
            category: audit_types_1.AuditEventCategory.AI_OPERATION,
            severity: log.status === 'error' ? audit_types_1.AuditEventSeverity.HIGH : audit_types_1.AuditEventSeverity.INFO,
            operationType: log.operationType,
            description: log.errorMessage || `AI operation ${log.operationType} completed with status ${log.status}`,
            timestamp: new Date(),
            metadata: {
                requestPayload: log.requestPayload,
                responsePayload: log.responsePayload,
                aiModelUsed: log.aiModelUsed,
                cost: log.cost,
                tokensUsed: log.tokensUsed,
                status: log.status,
                errorMessage: log.errorMessage,
            },
        });
    }
}
exports.AigencyRepository = AigencyRepository;
