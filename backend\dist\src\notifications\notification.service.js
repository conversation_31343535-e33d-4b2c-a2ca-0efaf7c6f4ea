"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationService = void 0;
const websocket_types_1 = require("../websocket/websocket.types");
class NotificationService {
    constructor(wsService, notificationRepository) {
        this.wsService = wsService;
        this.notificationRepository = notificationRepository;
    }
    async createNotification(notificationData) {
        const newNotification = await this.notificationRepository.createNotification(notificationData);
        this.emitNotification(newNotification);
        return newNotification;
    }
    emitNotification(notification) {
        const payload = {
            userId: notification.userId,
            message: notification.message,
            type: notification.type,
            timestamp: notification.createdAt,
            read: notification.read,
        };
        if (notification.userId) {
            this.wsService.sendToUser(notification.userId, JSON.stringify({
                event: websocket_types_1.WebSocketEvent.NOTIFICATION,
                payload,
            }));
        }
        else {
            this.wsService.broadcast(JSON.stringify({
                event: websocket_types_1.WebSocketEvent.NOTIFICATION,
                payload,
            }));
        }
    }
    async getNotificationsForUser(userId, read, page, size, sort) {
        const { notifications, total } = await this.notificationRepository.getNotifications(userId, read, page, size, sort);
        return { data: notifications, pagination: { total, page: page || 1, size: size || 10 } };
    }
    async markNotificationAsRead(notificationId) {
        const updatedNotification = await this.notificationRepository.updateNotification(notificationId, { read: true });
        if (!updatedNotification) {
            throw new Error('Notification not found');
        }
        return updatedNotification;
    }
}
exports.NotificationService = NotificationService;
