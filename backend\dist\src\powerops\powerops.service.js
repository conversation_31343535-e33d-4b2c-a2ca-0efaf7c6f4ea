"use strict";
var __esDecorate = (this && this.__esDecorate) || function (ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {
    function accept(f) { if (f !== void 0 && typeof f !== "function") throw new TypeError("Function expected"); return f; }
    var kind = contextIn.kind, key = kind === "getter" ? "get" : kind === "setter" ? "set" : "value";
    var target = !descriptorIn && ctor ? contextIn["static"] ? ctor : ctor.prototype : null;
    var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});
    var _, done = false;
    for (var i = decorators.length - 1; i >= 0; i--) {
        var context = {};
        for (var p in contextIn) context[p] = p === "access" ? {} : contextIn[p];
        for (var p in contextIn.access) context.access[p] = contextIn.access[p];
        context.addInitializer = function (f) { if (done) throw new TypeError("Cannot add initializers after decoration has completed"); extraInitializers.push(accept(f || null)); };
        var result = (0, decorators[i])(kind === "accessor" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);
        if (kind === "accessor") {
            if (result === void 0) continue;
            if (result === null || typeof result !== "object") throw new TypeError("Object expected");
            if (_ = accept(result.get)) descriptor.get = _;
            if (_ = accept(result.set)) descriptor.set = _;
            if (_ = accept(result.init)) initializers.unshift(_);
        }
        else if (_ = accept(result)) {
            if (kind === "field") initializers.unshift(_);
            else descriptor[key] = _;
        }
    }
    if (target) Object.defineProperty(target, contextIn.name, descriptor);
    done = true;
};
var __runInitializers = (this && this.__runInitializers) || function (thisArg, initializers, value) {
    var useValue = arguments.length > 2;
    for (var i = 0; i < initializers.length; i++) {
        value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);
    }
    return useValue ? value : void 0;
};
var __setFunctionName = (this && this.__setFunctionName) || function (f, name, prefix) {
    if (typeof name === "symbol") name = name.description ? "[".concat(name.description, "]") : "";
    return Object.defineProperty(f, "name", { configurable: true, value: prefix ? "".concat(prefix, " ", name) : name });
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PowerOpsService = void 0;
const common_1 = require("@nestjs/common");
const powerops_types_1 = require("./powerops.types");
let PowerOpsService = (() => {
    let _classDecorators = [(0, common_1.Injectable)()];
    let _classDescriptor;
    let _classExtraInitializers = [];
    let _classThis;
    var PowerOpsService = _classThis = class {
        async getAchievementById(achievementId) {
            return {
                id: '1',
                name: 'Sample Achievement',
                description: 'Sample Description',
                xpReward: 100,
                criteria: {
                    target: 10,
                    requiredAchievementId: null,
                    requiredStreakDay: 5
                }
            };
        }
        async getAchievement(userId, entityType) {
            return {
                userId,
                achievementId: '1',
                progress: 5,
                completed: false,
                completedAt: null
            };
        }
        async grantAchievement(userId, achievementId) {
            return {
                userId,
                achievementId,
                grantedAt: new Date()
            };
        }
    };
    __setFunctionName(_classThis, "PowerOpsService");
    (() => {
        const _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(null) : void 0;
        __esDecorate(null, _classDescriptor = { value: _classThis }, _classDecorators, { kind: "class", name: _classThis.name, metadata: _metadata }, null, _classExtraInitializers);
        PowerOpsService = _classThis = _classDescriptor.value;
        if (_metadata) Object.defineProperty(_classThis, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        __runInitializers(_classThis, _classExtraInitializers);
    })();
    return PowerOpsService = _classThis;
})();
exports.PowerOpsService = PowerOpsService;
let PowerOpsService = (() => {
    let _classDecorators = [(0, common_1.Injectable)()];
    let _classDescriptor;
    let _classExtraInitializers = [];
    let _classThis;
    var PowerOpsService = _classThis = class {
        async getAchievementById(achievementId) {
            return {
                id: '1',
                name: 'Sample Achievement',
                description: 'Sample Description',
                xpReward: 100,
                criteria: {
                    target: 10,
                    requiredAchievementId: null,
                    requiredStreakDay: 5
                }
            };
        }
        async getAchievement(userId, entityType) {
            return {
                userId,
                achievementId: '1',
                progress: 5,
                completed: false,
                completedAt: null
            };
        }
        async grantAchievement(userId, achievementId) {
            return {
                userId,
                achievementId,
                grantedAt: new Date()
            };
        }
    };
    __setFunctionName(_classThis, "PowerOpsService");
    (() => {
        const _metadata = typeof Symbol === "function" && Symbol.metadata ? Object.create(null) : void 0;
        __esDecorate(null, _classDescriptor = { value: _classThis }, _classDecorators, { kind: "class", name: _classThis.name, metadata: _metadata }, null, _classExtraInitializers);
        PowerOpsService = _classThis = _classDescriptor.value;
        if (_metadata) Object.defineProperty(_classThis, Symbol.metadata, { enumerable: true, configurable: true, writable: true, value: _metadata });
        __runInitializers(_classThis, _classExtraInitializers);
    })();
    return PowerOpsService = _classThis;
})();
exports.PowerOpsService = PowerOpsService;
const powerops_repository_1 = require("./powerops.repository");
const db_1 = __importDefault(require("../database/db"));
const authorization_service_1 = require("../auth/authorization.service");
const permissions_1 = require("../auth/permissions");
const notification_service_1 = require("../notifications/notification.service");
const powerops_types_2 = require("./powerops.types");
const errors_1 = require("../utils/errors");
class PowerOpsService {
    constructor(powerOpsRepository, authorizationService, notificationService) {
        this.powerOpsRepository = powerOpsRepository || new powerops_repository_1.PowerOpsRepository(db_1.default);
        this.authorizationService = authorizationService || new authorization_service_1.AuthorizationService();
        this.notificationService = notificationService || new notification_service_1.NotificationService({}, {});
    }
    async scaleServiceResources(serviceName, direction) {
        console.log(`Simulating scaling ${direction} for service: ${serviceName}`);
    }
    async logUsage(user, data) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.POWER_OPS_CREATE);
        const usageEntry = await this.powerOpsRepository.logPowerOpsUsage(data);
        return {
            entityId: usageEntry.entityId,
            entityType: usageEntry.entityType,
            totalUsageUnits: usageEntry.usageUnits,
            estimatedCost: usageEntry.estimatedCost,
        };
    }
    async getUsage(user, entityId, entityType, startDate, endDate) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.POWER_OPS_READ);
        let resourceOwnerId;
        let resourceOrganizationId;
        let resourceTeamId;
        if (entityType === powerops_types_1.EntityType.PaimInstance) {
            resourceOwnerId = entityId;
            resourceOrganizationId = user.organizationId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === powerops_types_1.EntityType.User) {
            resourceOwnerId = entityId;
            resourceOrganizationId = user.organizationId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === powerops_types_1.EntityType.Organization) {
            resourceOrganizationId = entityId;
            resourceOwnerId = user.userId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === powerops_types_1.EntityType.Team) {
            resourceTeamId = entityId;
            resourceOwnerId = user.userId;
            resourceOrganizationId = user.organizationId;
        }
        if (resourceOwnerId || resourceOrganizationId || resourceTeamId) {
            this.authorizationService.canAccessResource(user, {
                ownerId: resourceOwnerId,
                organizationId: resourceOrganizationId,
                teamId: resourceTeamId,
            }, 'read');
        }
        else {
            this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.POWER_OPS_READ_ALL);
        }
        const usageEntries = await this.powerOpsRepository.getPowerOpsUsage(entityId, entityType, startDate, endDate);
        const aggregatedUsage = {};
        usageEntries.forEach(entry => {
            const key = `${entry.entityId}-${entry.entityType}`;
            if (!aggregatedUsage[key]) {
                aggregatedUsage[key] = {
                    entityId: entry.entityId,
                    entityType: entry.entityType,
                    totalUsageUnits: 0,
                    estimatedCost: 0,
                };
            }
            aggregatedUsage[key].totalUsageUnits += entry.usageUnits;
            aggregatedUsage[key].estimatedCost += entry.estimatedCost;
        });
        return Object.values(aggregatedUsage);
    }
    async getBudgets(user, entityId, entityType) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.BILLING_VIEW);
        let resourceOwnerId;
        let resourceOrganizationId;
        let resourceTeamId;
        if (entityType === powerops_types_1.EntityType.PaimInstance) {
            resourceOwnerId = entityId;
            resourceOrganizationId = user.organizationId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === powerops_types_1.EntityType.User) {
            resourceOwnerId = entityId;
            resourceOrganizationId = user.organizationId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === powerops_types_1.EntityType.Organization) {
            resourceOrganizationId = entityId;
            resourceOwnerId = user.userId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === powerops_types_1.EntityType.Team) {
            resourceTeamId = entityId;
            resourceOwnerId = user.userId;
            resourceOrganizationId = user.organizationId;
        }
        if (resourceOwnerId || resourceOrganizationId || resourceTeamId) {
            this.authorizationService.canAccessResource(user, {
                ownerId: resourceOwnerId,
                organizationId: resourceOrganizationId,
                teamId: resourceTeamId,
            }, 'read');
        }
        else {
            this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.BILLING_VIEW_ALL);
        }
        return this.powerOpsRepository.getBudgets(entityId, entityType);
    }
    async createBudget(user, data) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.BILLING_MANAGE);
        this.authorizationService.canAccessResource(user, {
            ownerId: data.entityType === powerops_types_1.EntityType.User ? data.entityId : undefined,
            organizationId: data.entityType === powerops_types_1.EntityType.Organization ? data.entityId : user.organizationId,
            teamId: data.entityType === powerops_types_1.EntityType.Team ? data.entityId : user.teamId,
        }, 'create');
        return this.powerOpsRepository.createBudget(data);
    }
    async updateBudget(user, budgetId, data) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.BILLING_MANAGE);
        const existingBudget = await this.powerOpsRepository.getBudgetById(budgetId);
        if (!existingBudget) {
            throw new errors_1.CustomError('Budget not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
        }
        this.authorizationService.canAccessResource(user, {
            ownerId: existingBudget.entityType === powerops_types_1.EntityType.User ? existingBudget.entityId : undefined,
            organizationId: existingBudget.entityType === powerops_types_1.EntityType.Organization ? existingBudget.entityId : user.organizationId,
            teamId: existingBudget.entityType === powerops_types_1.EntityType.Team ? existingBudget.entityId : user.teamId,
        }, 'update');
        const updatedBudget = await this.powerOpsRepository.updateBudget(budgetId, data);
        if (!updatedBudget) {
            throw new errors_1.CustomError('Budget not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
        }
        return updatedBudget;
    }
    async deleteBudget(user, budgetId) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.BILLING_MANAGE);
        const existingBudget = await this.powerOpsRepository.getBudgetById(budgetId);
        if (!existingBudget) {
            throw new errors_1.CustomError('Budget not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
        }
        this.authorizationService.canAccessResource(user, {
            ownerId: existingBudget.entityType === powerops_types_1.EntityType.User ? existingBudget.entityId : undefined,
            organizationId: existingBudget.entityType === powerops_types_1.EntityType.Organization ? existingBudget.entityId : user.organizationId,
            teamId: existingBudget.entityType === powerops_types_1.EntityType.Team ? existingBudget.entityId : user.teamId,
        }, 'delete');
        const deleted = await this.powerOpsRepository.deleteBudget(budgetId);
        if (!deleted) {
            throw new errors_1.CustomError('Budget not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
        }
        return deleted;
    }
    async getCostOptimizationRecommendations(user, entityId, entityType) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.BILLING_VIEW);
        let resourceOwnerId;
        let resourceOrganizationId;
        let resourceTeamId;
        if (entityType === powerops_types_1.EntityType.PaimInstance) {
            resourceOwnerId = entityId;
            resourceOrganizationId = user.organizationId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === powerops_types_1.EntityType.User) {
            resourceOwnerId = entityId;
            resourceOrganizationId = user.organizationId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === powerops_types_1.EntityType.Organization) {
            resourceOrganizationId = entityId;
            resourceOwnerId = user.userId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === powerops_types_1.EntityType.Team) {
            resourceTeamId = entityId;
            resourceOwnerId = user.userId;
            resourceOrganizationId = user.organizationId;
        }
        if (resourceOwnerId || resourceOrganizationId || resourceTeamId) {
            this.authorizationService.canAccessResource(user, {
                ownerId: resourceOwnerId,
                organizationId: resourceOrganizationId,
                teamId: resourceTeamId,
            }, 'read');
        }
        else {
            this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.BILLING_VIEW_ALL);
        }
        return this.powerOpsRepository.getCostOptimizationRecommendations(entityId, entityType);
    }
    async getResourceUsageLimits(user, entityId, entityType) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.BILLING_VIEW);
        let resourceOwnerId;
        let resourceOrganizationId;
        let resourceTeamId;
        if (entityType === powerops_types_1.EntityType.PaimInstance) {
            resourceOwnerId = entityId;
            resourceOrganizationId = user.organizationId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === powerops_types_1.EntityType.User) {
            resourceOwnerId = entityId;
            resourceOrganizationId = user.organizationId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === powerops_types_1.EntityType.Organization) {
            resourceOrganizationId = entityId;
            resourceOwnerId = user.userId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === powerops_types_1.EntityType.Team) {
            resourceTeamId = entityId;
            resourceOwnerId = user.userId;
            resourceOrganizationId = user.organizationId;
        }
        if (resourceOwnerId || resourceOrganizationId || resourceTeamId) {
            this.authorizationService.canAccessResource(user, {
                ownerId: resourceOwnerId,
                organizationId: resourceOrganizationId,
                teamId: resourceTeamId,
            }, 'read');
        }
        else {
            this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.BILLING_VIEW_ALL);
        }
        return this.powerOpsRepository.getResourceUsageLimits(entityId, entityType);
    }
    async setResourceUsageLimit(user, data) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.BILLING_MANAGE);
        this.authorizationService.canAccessResource(user, {
            ownerId: data.entityType === powerops_types_1.EntityType.User ? data.entityId : undefined,
            organizationId: data.entityType === powerops_types_1.EntityType.Organization ? data.entityId : user.organizationId,
            teamId: data.entityType === powerops_types_1.EntityType.Team ? data.entityId : user.teamId,
        }, 'manage');
        return this.powerOpsRepository.setResourceUsageLimit(data);
    }
    async getXp(user, id, entityType) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.POWER_OPS_READ);
        let resourceOwnerId;
        let resourceOrganizationId;
        let resourceTeamId;
        if (entityType === powerops_types_1.EntityType.PaimInstance) {
            resourceOwnerId = id;
            resourceOrganizationId = user.organizationId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === powerops_types_1.EntityType.User) {
            resourceOwnerId = id;
            resourceOrganizationId = user.organizationId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === powerops_types_1.EntityType.Organization) {
            resourceOrganizationId = id;
            resourceOwnerId = user.userId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === powerops_types_1.EntityType.Team) {
            resourceTeamId = id;
            resourceOwnerId = user.userId;
            resourceOrganizationId = user.organizationId;
        }
        if (resourceOwnerId || resourceOrganizationId || resourceTeamId) {
            this.authorizationService.canAccessResource(user, {
                ownerId: resourceOwnerId,
                organizationId: resourceOrganizationId,
                teamId: resourceTeamId,
            }, 'read');
        }
        else {
            this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.POWER_OPS_READ_ALL);
        }
        const xp = await this.powerOpsRepository.getXp(id, entityType);
        if (!xp) {
            return { entityId: id, entityType, currentXp: 0, level: 1 };
        }
        return xp;
    }
    async awardXp(user, data) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.POWER_OPS_MANAGE_ALL);
        this.authorizationService.canAccessResource(user, {
            ownerId: data.entityType === powerops_types_1.EntityType.User ? data.entityId : undefined,
            organizationId: data.entityType === powerops_types_1.EntityType.Organization ? data.entityId : user.organizationId,
            teamId: data.entityType === powerops_types_1.EntityType.Team ? data.entityId : user.teamId,
        }, 'manage');
        const updatedXp = await this.powerOpsRepository.awardXp(data);
        if (updatedXp.level > (await this.powerOpsRepository.getXp(data.entityId, data.entityType))?.level || 0) {
            await this.notificationService.createNotification({
                userId: data.entityId,
                type: 'success',
                message: `Congratulations! You've reached PowerOps Level ${updatedXp.level}!`,
            });
            this.notificationService.wsService.broadcast(JSON.stringify({
                event: 'powerOpsAchievement',
                payload: {
                    userId: data.entityId,
                    achievementName: `Level Up: ${updatedXp.level}`,
                    levelUp: true,
                },
            }));
        }
        return updatedXp;
    }
    async getAllBadges(user) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.POWER_OPS_READ);
        return this.powerOpsRepository.getAllBadges();
    }
    async getBadges(user, entityId, entityType) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.POWER_OPS_READ);
        let resourceOwnerId;
        let resourceOrganizationId;
        let resourceTeamId;
        if (entityType === powerops_types_1.EntityType.PaimInstance) {
            resourceOwnerId = entityId;
            resourceOrganizationId = user.organizationId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === powerops_types_1.EntityType.User) {
            resourceOwnerId = entityId;
            resourceOrganizationId = user.organizationId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === powerops_types_1.EntityType.Organization) {
            resourceOrganizationId = entityId;
            resourceOwnerId = user.userId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === powerops_types_1.EntityType.Team) {
            resourceTeamId = entityId;
            resourceOwnerId = user.userId;
            resourceOrganizationId = user.organizationId;
        }
        if (resourceOwnerId || resourceOrganizationId || resourceTeamId) {
            this.authorizationService.canAccessResource(user, {
                ownerId: resourceOwnerId,
                organizationId: resourceOrganizationId,
                teamId: resourceTeamId,
            }, 'read');
        }
        else {
            this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.POWER_OPS_READ_ALL);
        }
        return this.powerOpsRepository.getBadgesForEntity(entityId, entityType);
    }
    async awardBadge(user, data) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.POWER_OPS_MANAGE_ALL);
        this.authorizationService.canAccessResource(user, {
            ownerId: data.entityType === powerops_types_1.EntityType.User ? data.entityId : undefined,
            organizationId: data.entityType === powerops_types_1.EntityType.Organization ? data.entityId : user.organizationId,
            teamId: data.entityType === powerops_types_1.EntityType.Team ? data.entityId : user.teamId,
        }, 'manage');
        return this.powerOpsRepository.awardBadge(data);
    }
    async evaluateBadgeEligibility(user) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.POWER_OPS_MANAGE_ALL);
        console.log('Evaluating badge eligibility for all users/organizations (MVP placeholder)...');
    }
    async getAchievements(user, entityId, entityType) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.POWER_OPS_READ);
        let resourceOwnerId;
        let resourceOrganizationId;
        let resourceTeamId;
        if (entityType === powerops_types_1.EntityType.PaimInstance) {
            resourceOwnerId = entityId;
            resourceOrganizationId = user.organizationId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === powerops_types_1.EntityType.User) {
            resourceOwnerId = entityId;
            resourceOrganizationId = user.organizationId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === powerops_types_1.EntityType.Organization) {
            resourceOrganizationId = entityId;
            resourceOwnerId = user.userId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === powerops_types_1.EntityType.Team) {
            resourceTeamId = entityId;
            resourceOwnerId = user.userId;
            resourceOrganizationId = user.organizationId;
        }
        if (resourceOwnerId || resourceOrganizationId || resourceTeamId) {
            this.authorizationService.canAccessResource(user, {
                ownerId: resourceOwnerId,
                organizationId: resourceOrganizationId,
                teamId: resourceTeamId,
            }, 'read');
        }
        else {
            this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.POWER_OPS_READ_ALL);
        }
        return this.powerOpsRepository.getAchievements(entityId, entityType);
    }
    async grantAchievement(user, data) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.POWER_OPS_MANAGE_ALL);
        this.authorizationService.canAccessResource(user, {
            ownerId: data.entityType === powerops_types_1.EntityType.User ? data.entityId : undefined,
            organizationId: data.entityType === powerops_types_1.EntityType.Organization ? data.entityId : user.organizationId,
            teamId: data.entityType === powerops_types_1.EntityType.Team ? data.entityId : user.teamId,
        }, 'manage');
        const newAchievement = await this.powerOpsRepository.grantAchievement(data);
        await this.notificationService.createNotification({
            userId: data.entityId,
            type: 'success',
            message: `Achievement Unlocked: ${newAchievement.name}!`,
        });
        this.notificationService.wsService.broadcast(JSON.stringify({
            event: 'powerOpsAchievement',
            payload: {
                userId: data.entityId,
                achievementName: newAchievement.name,
                levelUp: false,
            },
        }));
        return newAchievement;
    }
    async getStreaks(user, entityId, entityType) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.POWER_OPS_READ);
        let resourceOwnerId;
        let resourceOrganizationId;
        let resourceTeamId;
        if (entityType === powerops_types_1.EntityType.PaimInstance) {
            resourceOwnerId = entityId;
            resourceOrganizationId = user.organizationId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === powerops_types_1.EntityType.User) {
            resourceOwnerId = entityId;
            resourceOrganizationId = user.organizationId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === powerops_types_1.EntityType.Organization) {
            resourceOrganizationId = entityId;
            resourceOwnerId = user.userId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === powerops_types_1.EntityType.Team) {
            resourceTeamId = entityId;
            resourceOwnerId = user.userId;
            resourceOrganizationId = user.organizationId;
        }
        if (resourceOwnerId || resourceOrganizationId || resourceTeamId) {
            this.authorizationService.canAccessResource(user, {
                ownerId: resourceOwnerId,
                organizationId: resourceOrganizationId,
                teamId: resourceTeamId,
            }, 'read');
        }
        else {
            this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.POWER_OPS_READ_ALL);
        }
        return this.powerOpsRepository.getStreaks(entityId, entityType);
    }
    async getLeaderboard(user, metric, limit) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.POWER_OPS_READ);
        return this.powerOpsRepository.getLeaderboard(metric, limit);
    }
    async getInvoices(user, entityId, entityType) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.BILLING_VIEW);
        let resourceOwnerId;
        let resourceOrganizationId;
        let resourceTeamId;
        if (entityType === powerops_types_1.EntityType.PaimInstance) {
            resourceOwnerId = entityId;
            resourceOrganizationId = user.organizationId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === powerops_types_1.EntityType.User) {
            resourceOwnerId = entityId;
            resourceOrganizationId = user.organizationId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === powerops_types_1.EntityType.Organization) {
            resourceOrganizationId = entityId;
            resourceOwnerId = user.userId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === powerops_types_1.EntityType.Team) {
            resourceTeamId = entityId;
            resourceOwnerId = user.userId;
            resourceOrganizationId = user.organizationId;
        }
        if (resourceOwnerId || resourceOrganizationId || resourceTeamId) {
            this.authorizationService.canAccessResource(user, {
                ownerId: resourceOwnerId,
                organizationId: resourceOrganizationId,
                teamId: resourceTeamId,
            }, 'read');
        }
        else {
            this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.BILLING_VIEW_ALL);
        }
        return this.powerOpsRepository.getInvoices(entityId, entityType);
    }
    async createInvoice(user, data) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.BILLING_MANAGE);
        this.authorizationService.canAccessResource(user, {
            ownerId: data.entityType === powerops_types_1.EntityType.User ? data.entityId : undefined,
            organizationId: data.entityType === powerops_types_1.EntityType.Organization ? data.entityId : user.organizationId,
            teamId: data.entityType === powerops_types_1.EntityType.Team ? data.entityId : user.teamId,
        }, 'create');
        return this.powerOpsRepository.createInvoice(data);
    }
    async processPayment(user, data) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.BILLING_MANAGE);
        this.authorizationService.canAccessResource(user, {
            ownerId: data.entityType === powerops_types_1.EntityType.User ? data.entityId : undefined,
            organizationId: data.entityType === powerops_types_1.EntityType.Organization ? data.entityId : user.organizationId,
            teamId: data.entityType === powerops_types_1.EntityType.Team ? data.entityId : user.teamId,
        }, 'manage');
        return this.powerOpsRepository.processPayment(data);
    }
    async getNotifications(user, entityId, entityType) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.POWER_OPS_READ);
        let resourceOwnerId;
        let resourceOrganizationId;
        let resourceTeamId;
        if (entityType === powerops_types_1.EntityType.PaimInstance) {
            resourceOwnerId = entityId;
            resourceOrganizationId = user.organizationId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === powerops_types_1.EntityType.User) {
            resourceOwnerId = entityId;
            resourceOrganizationId = user.organizationId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === powerops_types_1.EntityType.Organization) {
            resourceOrganizationId = entityId;
            resourceOwnerId = user.userId;
            resourceTeamId = user.teamId;
        }
        else if (entityType === powerops_types_1.EntityType.Team) {
            resourceTeamId = entityId;
            resourceOwnerId = user.userId;
            resourceOrganizationId = user.organizationId;
        }
        if (resourceOwnerId || resourceOrganizationId || resourceTeamId) {
            this.authorizationService.canAccessResource(user, {
                ownerId: resourceOwnerId,
                organizationId: resourceOrganizationId,
                teamId: resourceTeamId,
            }, 'read');
        }
        else {
            this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.POWER_OPS_READ_ALL);
        }
        return this.powerOpsRepository.getNotifications(entityId, entityType);
    }
    async createNotification(user, data) {
        this.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.POWER_OPS_CREATE);
        this.authorizationService.canAccessResource(user, {
            ownerId: data.entityType === powerops_types_1.EntityType.User ? data.entityId : undefined,
            organizationId: data.entityType === powerops_types_1.EntityType.Organization ? data.entityId : user.organizationId,
            teamId: data.entityType === powerops_types_1.EntityType.Team ? data.entityId : user.teamId,
        }, 'create');
        return this.powerOpsRepository.createNotification(data);
    }
    calculateUsageCost(usageUnits, costCategory) {
        switch (costCategory) {
            case powerops_types_2.CostCategory.Compute: return usageUnits * 0.01;
            case powerops_types_2.CostCategory.Storage: return usageUnits * 0.001;
            case powerops_types_2.CostCategory.Bandwidth: return usageUnits * 0.005;
            case powerops_types_2.CostCategory.AIModel: return usageUnits * 0.05;
            default: return usageUnits * 0.01;
        }
    }
}
exports.PowerOpsService = PowerOpsService;
