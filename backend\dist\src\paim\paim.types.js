"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CrossPaimCommunicationMessageType = exports.PaimTierChangeRequestStatusEnum = exports.PaimInstanceStatus = exports.PaimTierEnum = void 0;
const db_1 = require("../types/db");
Object.defineProperty(exports, "PaimTierEnum", { enumerable: true, get: function () { return db_1.PaimTierEnum; } });
var PaimInstanceStatus;
(function (PaimInstanceStatus) {
    PaimInstanceStatus["Active"] = "active";
    PaimInstanceStatus["Inactive"] = "inactive";
    PaimInstanceStatus["Suspended"] = "suspended";
})(PaimInstanceStatus || (exports.PaimInstanceStatus = PaimInstanceStatus = {}));
var PaimTierChangeRequestStatusEnum;
(function (PaimTierChangeRequestStatusEnum) {
    PaimTierChangeRequestStatusEnum["Pending"] = "pending";
    PaimTierChangeRequestStatusEnum["Approved"] = "approved";
    PaimTierChangeRequestStatusEnum["Rejected"] = "rejected";
    PaimTierChangeRequestStatusEnum["Completed"] = "completed";
})(PaimTierChangeRequestStatusEnum || (exports.PaimTierChangeRequestStatusEnum = PaimTierChangeRequestStatusEnum = {}));
var CrossPaimCommunicationMessageType;
(function (CrossPaimCommunicationMessageType) {
    CrossPaimCommunicationMessageType["Text"] = "text";
    CrossPaimCommunicationMessageType["Command"] = "command";
    CrossPaimCommunicationMessageType["DataTransfer"] = "data_transfer";
})(CrossPaimCommunicationMessageType || (exports.CrossPaimCommunicationMessageType = CrossPaimCommunicationMessageType = {}));
