"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getLeaderboardQuerySchema = exports.getPowerOpsUsageQuerySchema = exports.entityIdEntityTypeQuerySchema = exports.createNotificationSchema = exports.setResourceUsageLimitSchema = exports.processPaymentSchema = exports.createInvoiceSchema = exports.updateBudgetSchema = exports.createBudgetSchema = exports.grantAchievementSchema = exports.awardBadgeSchema = exports.awardXpSchema = exports.logPowerOpsUsageSchema = void 0;
const joi_1 = __importDefault(require("joi"));
const powerops_types_1 = require("./powerops.types");
exports.logPowerOpsUsageSchema = joi_1.default.object({
    entityId: joi_1.default.string().guid({ version: 'uuidv4' }).required(),
    entityType: joi_1.default.string().valid(...Object.values(powerops_types_1.EntityType)).required(),
    usageUnits: joi_1.default.number().min(0).required(),
    costCategory: joi_1.default.string().valid(...Object.values(powerops_types_1.CostCategory)).required(),
    description: joi_1.default.string().trim().max(500).optional().allow(null, ''),
    timestamp: joi_1.default.string().isoDate().required(),
});
exports.awardXpSchema = joi_1.default.object({
    entityId: joi_1.default.string().guid({ version: 'uuidv4' }).required(),
    entityType: joi_1.default.string().valid(...Object.values(powerops_types_1.EntityType)).required(),
    org_id: joi_1.default.string().required(),
    agent_id: joi_1.default.string().optional().allow(null, ''),
    powerops: joi_1.default.number().integer().min(1).required(),
    reason: joi_1.default.string().trim().min(3).max(200).required(),
    metadata: joi_1.default.object().optional(),
});
exports.awardBadgeSchema = joi_1.default.object({
    entityId: joi_1.default.string().guid({ version: 'uuidv4' }).required(),
    entityType: joi_1.default.string().valid(...Object.values(powerops_types_1.EntityType)).required(),
    badgeId: joi_1.default.string().guid({ version: 'uuidv4' }).required(),
});
exports.grantAchievementSchema = joi_1.default.object({
    entityId: joi_1.default.string().guid({ version: 'uuidv4' }).required(),
    entityType: joi_1.default.string().valid(...Object.values(powerops_types_1.EntityType)).required(),
    achievementId: joi_1.default.string().guid({ version: 'uuidv4' }).required(),
});
exports.createBudgetSchema = joi_1.default.object({
    entityId: joi_1.default.string().guid({ version: 'uuidv4' }).required(),
    entityType: joi_1.default.string().valid(...Object.values(powerops_types_1.EntityType)).required(),
    monthlyLimit: joi_1.default.number().min(0).required(),
    currency: joi_1.default.string().length(3).uppercase().required(),
    alertThreshold: joi_1.default.number().min(0).max(100).optional(),
});
exports.updateBudgetSchema = joi_1.default.object({
    monthlyLimit: joi_1.default.number().min(0).optional(),
    alertThreshold: joi_1.default.number().min(0).max(100).optional(),
});
exports.createInvoiceSchema = joi_1.default.object({
    entityId: joi_1.default.string().guid({ version: 'uuidv4' }).required(),
    entityType: joi_1.default.string().valid(...Object.values(powerops_types_1.EntityType)).required(),
    billingPeriodStart: joi_1.default.string().isoDate().required(),
    billingPeriodEnd: joi_1.default.string().isoDate().required(),
    lineItems: joi_1.default.array().items(joi_1.default.object({
        description: joi_1.default.string().required(),
        quantity: joi_1.default.number().min(0).required(),
        unitPrice: joi_1.default.number().min(0).required(),
        total: joi_1.default.number().min(0).required(),
    })).min(1).required(),
    paymentMethodId: joi_1.default.string().optional().allow(null, ''),
});
exports.processPaymentSchema = joi_1.default.object({
    invoiceId: joi_1.default.string().guid({ version: 'uuidv4' }).required(),
    amount: joi_1.default.number().min(0.01).required(),
    currency: joi_1.default.string().length(3).uppercase().required(),
    paymentMethod: joi_1.default.string().required(),
    transactionDetails: joi_1.default.object().optional(),
});
exports.setResourceUsageLimitSchema = joi_1.default.object({
    entityId: joi_1.default.string().guid({ version: 'uuidv4' }).required(),
    entityType: joi_1.default.string().valid(...Object.values(powerops_types_1.EntityType)).required(),
    resourceType: joi_1.default.string().required(),
    limit: joi_1.default.number().min(0).required(),
    unit: joi_1.default.string().required(),
});
exports.createNotificationSchema = joi_1.default.object({
    entityId: joi_1.default.string().guid({ version: 'uuidv4' }).required(),
    entityType: joi_1.default.string().valid(...Object.values(powerops_types_1.EntityType)).required(),
    type: joi_1.default.string().required(),
    message: joi_1.default.string().min(1).max(1000).required(),
});
exports.entityIdEntityTypeQuerySchema = joi_1.default.object({
    entityId: joi_1.default.string().guid({ version: 'uuidv4' }).required(),
    entityType: joi_1.default.string().valid(...Object.values(powerops_types_1.EntityType)).required(),
});
exports.getPowerOpsUsageQuerySchema = exports.entityIdEntityTypeQuerySchema.keys({
    startDate: joi_1.default.string().isoDate().optional(),
    endDate: joi_1.default.string().isoDate().optional(),
});
exports.getLeaderboardQuerySchema = joi_1.default.object({
    metric: joi_1.default.string().required(),
    limit: joi_1.default.number().integer().min(1).max(100).optional(),
});
