{"name": "backend", "lockfileVersion": 3, "requires": true, "packages": {"..": {"name": "theaigency-backend", "version": "1.0.0", "license": "ISC", "dependencies": {"@supabase/supabase-js": "^2.49.8", "appwrite": "^16.1.0", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "express-rate-limit": "^7.4.1", "express-validator": "^7.2.1", "helmet": "^7.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "knex": "^3.1.0", "pg": "^8.11.5", "uuid": "^9.0.1", "winston": "^3.13.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/express-rate-limit": "^6.0.0", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^20.12.12", "@types/pg": "^8.11.6", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.15.0", "@typescript-eslint/parser": "^8.15.0", "eslint": "^9.15.0", "eslint-config-prettier": "^10.1.5", "nodemon": "^3.1.0", "prettier": "^3.2.5", "ts-node": "^10.9.2", "typescript": "^5.4.5", "vitest": "^3.1.4"}}, "node_modules/theaigency-backend": {"resolved": "..", "link": true}, "node_modules/zod": {"version": "3.25.31", "resolved": "https://registry.npmjs.org/zod/-/zod-3.25.31.tgz", "integrity": "sha512-SVCtUpZ1D9TFcRnCY7wlCpGEzV4+2HlXnJiwrgq1T93m98BIT6M5lbxRrEm7v6pYZXV4QNQo1t2KVv93JXk6XA==", "license": "MIT", "funding": {"url": "https://github.com/sponsors/colinhacks"}}}}