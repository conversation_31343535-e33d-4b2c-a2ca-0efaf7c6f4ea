"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PERMISSIONS = void 0;
exports.PERMISSIONS = {
    PAIM_CREATE: 'paim:create',
    PAIM_READ: 'paim:read',
    PAIM_UPDATE: 'paim:update',
    PAIM_DELETE: 'paim:delete',
    PAIM_MANAGE_TIERS: 'paim:manage_tiers',
    PAIM_VIEW_ALL: 'paim:view_all',
    POWER_OPS_CREATE: 'power_ops:create',
    POWER_OPS_READ: 'power_ops:read',
    POWER_OPS_UPDATE: 'power_ops:update',
    POWER_OPS_DELETE: 'power_ops:delete',
    POWER_OPS_MANAGE_ALL: 'power_ops:manage_all',
    POWER_OPS_READ_ALL: 'power_ops:read_all',
    WORKFLOW_CREATE: 'workflow:create',
    WORKFLOW_READ: 'workflow:read',
    WORKFLOW_UPDATE: 'workflow:update',
    WORKFLOW_DELETE: 'workflow:delete',
    WORKFLOW_MANAGE_ALL: 'workflow:manage_all',
    AGENT_CREATE: 'agent:create',
    AGENT_READ: 'agent:read',
    AGENT_UPDATE: 'agent:update',
    AGENT_DELETE: 'agent:delete',
    AGENT_MANAGE_ALL: 'agent:manage_all',
    ORG_CREATE: 'org:create',
    ORG_READ: 'org:read',
    ORG_UPDATE: 'org:update',
    ORG_DELETE: 'org:delete',
    ORG_MANAGE_MEMBERS: 'org:manage_members',
    ORG_MANAGE_ROLES: 'org:manage_roles',
    ORG_VIEW_ALL: 'org:view_all',
    BILLING_VIEW: 'billing:view',
    BILLING_MANAGE: 'billing:manage',
    BILLING_VIEW_ALL: 'billing:view_all',
    AUDIT_VIEW: 'audit:view',
    AUDIT_MANAGE: 'audit:manage',
    SYSTEM_ADMIN_ACCESS: 'system:admin_access',
    SYSTEM_VIEW_LOGS: 'system:view_logs',
    SYSTEM_MANAGE_USERS: 'system:manage_users',
    SYSTEM_MANAGE_SETTINGS: 'system:manage_settings',
};
