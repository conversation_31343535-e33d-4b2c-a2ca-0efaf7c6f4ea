"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.authorizationService = exports.AuthorizationService = void 0;
const roles_1 = require("./roles");
const permissions_1 = require("./permissions");
const paim_permissions_1 = require("../paim/paim.permissions");
class AuthorizationService {
    hasRole(userRoles, requiredRole) {
        return userRoles.includes(requiredRole);
    }
    hasPermission(userRoles, userPaimTier, requiredPermission) {
        for (const role of userRoles) {
            const permissionsForRole = roles_1.ROLE_PERMISSIONS[role];
            if (permissionsForRole && permissionsForRole.includes(requiredPermission)) {
                return true;
            }
        }
        const paimTierPermissions = paim_permissions_1.PAIM_TIER_PERMISSIONS[userPaimTier];
        if (paimTierPermissions && paimTierPermissions.includes(requiredPermission)) {
            return true;
        }
        return false;
    }
    hasAnyPermission(userRoles, userPaimTier, requiredPermissions) {
        for (const permission of requiredPermissions) {
            if (this.hasPermission(userRoles, userPaimTier, permission)) {
                return true;
            }
        }
        return false;
    }
    hasAllPermissions(userRoles, userPaimTier, requiredPermissions) {
        for (const permission of requiredPermissions) {
            if (!this.hasPermission(userRoles, userPaimTier, permission)) {
                return false;
            }
        }
        return true;
    }
    canAccessResource(user, resource, action) {
        if (resource.ownerId === user.userId) {
            return true;
        }
        if (resource.organizationId === user.tenantId) {
            if (user.roles.includes(roles_1.UserRole.OrgAdmin) || user.roles.includes(roles_1.UserRole.SuperAdmin)) {
                return true;
            }
            if (resource.teamId && user.teamId && resource.teamId === user.teamId) {
                return true;
            }
        }
        if (this.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.SYSTEM_ADMIN_ACCESS)) {
            return true;
        }
        return false;
    }
}
exports.AuthorizationService = AuthorizationService;
exports.authorizationService = new AuthorizationService();
