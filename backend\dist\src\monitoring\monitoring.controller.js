"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MonitoringController = void 0;
const express = __importStar(require("express"));
const monitoring_service_1 = require("./monitoring.service");
const monitoring_repository_1 = require("./monitoring.repository");
const audit_service_1 = require("../audit/audit.service");
const paim_service_1 = require("../paim/paim.service");
const powerops_service_1 = require("../powerops/powerops.service");
const cultural_sensitivity_service_1 = require("../cultural-sensitivity/cultural-sensitivity.service");
const db_1 = __importDefault(require("../database/db"));
const errors_1 = require("../utils/errors");
const logger_1 = __importDefault(require("../config/logger"));
const creative_api_service_1 = require("../creative-api/creative-api.service");
const http_client_service_1 = require("../utils/http-client.service");
const type_guards_1 = require("../utils/type-guards");
class MonitoringController {
    constructor() {
        this.getHealthStatus = async (req, res, next) => {
            try {
                const dbStatus = await db_1.default.raw('SELECT 1');
                const creativeApiHealth = await this.creativeApiService.checkProviderHealth();
                const httpClientStatus = http_client_service_1.defaultHttpClient ? 'initialized' : 'uninitialized';
                const overallHealth = dbStatus && creativeApiHealth.every(p => p.status === 'healthy') && httpClientStatus === 'initialized' ? 'healthy' : 'degraded';
                const uptime = process.uptime();
                res.status(200).json({
                    status: overallHealth,
                    database: dbStatus ? 'connected' : 'disconnected',
                    creativeApis: creativeApiHealth,
                    httpClient: httpClientStatus,
                    uptime: `${Math.floor(uptime / 3600)}h ${Math.floor((uptime % 3600) / 60)}m ${Math.floor(uptime % 60)}s`,
                    version: process.env.APP_VERSION || '1.0.0',
                });
            }
            catch (error) {
                console.error('Health check failed:', error);
                res.status(500).json({ status: 'unhealthy', error: error.message });
            }
        };
        this.getReadinessStatus = async (req, res, next) => {
            try {
                const dbReady = await db_1.default.raw('SELECT 1').then(() => true).catch(() => false);
                const creativeApiReady = await this.creativeApiService.checkProviderHealth().then(statuses => statuses.every(p => p.status === 'healthy')).catch(() => false);
                const httpClientReady = http_client_service_1.defaultHttpClient ? true : false;
                const isReady = dbReady && creativeApiReady && httpClientReady;
                res.status(isReady ? 200 : 503).json({
                    status: isReady ? 'ready' : 'not_ready',
                    database: dbReady ? 'ready' : 'not_ready',
                    creativeApis: creativeApiReady ? 'ready' : 'not_ready',
                    httpClient: httpClientReady ? 'ready' : 'not_ready',
                });
            }
            catch (error) {
                console.error('Readiness check failed:', error);
                res.status(503).json({ status: 'not_ready', error: error.message });
            }
        };
        this.getHttpClientMetrics = async (req, res, next) => {
            try {
                const metrics = {
                    totalRequests: http_client_service_1.defaultHttpClient.getMetrics().length,
                    averageResponseTime: http_client_service_1.defaultHttpClient.getAverageResponseTime(),
                    errorRate: http_client_service_1.defaultHttpClient.getErrorRate(),
                    rawMetrics: http_client_service_1.defaultHttpClient.getMetrics(),
                };
                res.status(200).json(metrics);
            }
            catch (error) {
                logger_1.default.error('Failed to retrieve HTTP client metrics:', error);
                next(error);
            }
        };
        this.recordSystemHealth = async (req, res, next) => {
            try {
                const healthData = req.body;
                const recordedHealth = await this.monitoringService.recordSystemHealth(healthData);
                res.status(201).json(recordedHealth);
            }
            catch (error) {
                next(error);
            }
        };
        this.recordPerformanceMetrics = async (req, res, next) => {
            try {
                const { serviceName, metrics } = req.body;
                const recordedMetrics = await this.monitoringService.recordPerformanceMetrics(serviceName, metrics);
                res.status(201).json(recordedMetrics);
            }
            catch (error) {
                next(error);
            }
        };
        this.getLatestSystemHealth = async (req, res, next) => {
            try {
                const { serviceName } = req.params;
                const health = await this.monitoringService['repository'].getLatestSystemHealth((0, type_guards_1.requireParam)(serviceName, 'serviceName'));
                if (!health) {
                    throw new errors_1.CustomError(`System health for service ${serviceName} not found.`, { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
                }
                res.status(200).json(health);
            }
            catch (error) {
                next(error);
            }
        };
        this.getActiveAlerts = async (req, res, next) => {
            try {
                const alerts = await this.monitoringService['repository'].getActiveMonitoringAlerts();
                res.status(200).json(alerts);
            }
            catch (error) {
                next(error);
            }
        };
        this.getCoveEscalations = async (req, res, next) => {
            try {
                const escalations = await this.monitoringService['repository'].getAuditLogs('COVE_ESCALATION_INITIATED', 'CoveEscalation');
                res.status(200).json(escalations);
            }
            catch (error) {
                next(error);
            }
        };
        this.triggerManualAlert = async (req, res, next) => {
            try {
                const alertData = req.body;
                const triggeredAlert = await this.monitoringService.triggerAlert(alertData);
                res.status(201).json(triggeredAlert);
            }
            catch (error) {
                next(error);
            }
        };
        this.resolveCoveEscalation = async (req, res, next) => {
            try {
                const { escalationId } = req.params;
                const { resolvedBy, details } = req.body;
                if (!resolvedBy) {
                    throw new errors_1.CustomError('Resolved by user is required.', { originalErrorCode: 'BadRequestError', originalStatusCode: 400 });
                }
                const resolvedEscalation = await this.monitoringService.resolveCoveEscalation((0, type_guards_1.requireParam)(escalationId, 'escalationId'), resolvedBy, details);
                if (!resolvedEscalation) {
                    throw new errors_1.CustomError(`Escalation with ID ${escalationId} not found.`, { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
                }
                res.status(200).json(resolvedEscalation);
            }
            catch (error) {
                next(error);
            }
        };
        const monitoringRepository = new monitoring_repository_1.MonitoringRepository();
        const auditTrailService = new audit_service_1.AuditTrailService();
        const paimService = new paim_service_1.PaimService(null, auditTrailService);
        const powerOpsService = new powerops_service_1.PowerOpsService();
        const culturalSensitivityService = new cultural_sensitivity_service_1.CulturalSensitivityService(db_1.default);
        this.creativeApiService = new creative_api_service_1.CreativeApiService(auditTrailService);
        this.monitoringService = new monitoring_service_1.MonitoringService(monitoringRepository, auditTrailService, paimService, powerOpsService, culturalSensitivityService);
        this.router = express.Router();
        this.initializeRoutes();
    }
    initializeRoutes() {
        this.router.get('/health', this.getHealthStatus.bind(this));
        this.router.get('/ready', this.getReadinessStatus.bind(this));
        this.router.post('/system-health', this.recordSystemHealth.bind(this));
        this.router.post('/performance-metrics', this.recordPerformanceMetrics.bind(this));
        this.router.get('/system-health/:serviceName', this.getLatestSystemHealth.bind(this));
        this.router.get('/alerts', this.getActiveAlerts.bind(this));
        this.router.get('/escalations', this.getCoveEscalations.bind(this));
        this.router.post('/alerts/trigger', this.triggerManualAlert.bind(this));
        this.router.put('/escalations/:escalationId/resolve', this.resolveCoveEscalation.bind(this));
        this.router.get('/metrics/http-client', this.getHttpClientMetrics.bind(this));
    }
}
exports.MonitoringController = MonitoringController;
