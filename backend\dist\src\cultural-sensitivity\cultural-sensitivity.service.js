"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CulturalSensitivityService = void 0;
const cultural_sensitivity_repository_1 = require("./cultural-sensitivity.repository");
const errors_1 = require("../utils/errors");
const logger_1 = __importDefault(require("../config/logger"));
class CulturalSensitivityService {
    constructor(db, culturalSensitivityRepository) {
        this.culturalSensitivityRepository = culturalSensitivityRepository || new cultural_sensitivity_repository_1.CulturalSensitivityRepository(db);
    }
    async getLocalizedMessage(key, locale, params) {
        logger_1.default.info(`Getting localized message for key: ${key}, locale: ${locale}`);
        const messages = {
            'escalation_notification': {
                'en': 'Escalation initiated for service: {serviceName}. Reason: {description}',
                'ar': 'تم تصعيد المشكلة للخدمة: {serviceName}. السبب: {description}',
            },
        };
        let message = messages[key]?.[locale] || messages[key]?.['en'] || `Localized message not found for key: ${key}`;
        if (params) {
            for (const paramKey in params) {
                message = message.replace(`{${paramKey}}`, params[paramKey]);
            }
        }
        return message;
    }
    async getLocalizationSettings(entityId, entityType) {
        logger_1.default.info(`Fetching localization settings for entityId: ${entityId}, entityType: ${entityType}`);
        const settings = await this.culturalSensitivityRepository.getLocalizationSettings(entityId, entityType);
        if (!settings) {
            throw new errors_1.CustomError('Localization settings not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
        }
        return settings;
    }
    async updateLocalizationSettings(entityId, entityType, settings) {
        logger_1.default.info(`Updating localization settings for entityId: ${entityId}, entityType: ${entityType}`);
        let existingSettings = await this.culturalSensitivityRepository.getLocalizationSettings(entityId, entityType);
        if (!existingSettings) {
            const newSettings = {
                entityId,
                entityType,
                preferredLanguage: settings.preferredLanguage || 'en-US',
                timezone: settings.timezone || 'UTC',
                dateFormat: settings.dateFormat || 'YYYY-MM-DD',
                timeFormat: settings.timeFormat || 'HH:mm',
                currency: settings.currency || 'USD',
                culturalPreferences: settings.culturalPreferences || {},
            };
            return this.culturalSensitivityRepository.createLocalizationSettings(newSettings);
        }
        else {
            return this.culturalSensitivityRepository.updateLocalizationSettings(entityId, entityType, settings);
        }
    }
    async getCulturalContext(locale, entityId, entityType) {
        logger_1.default.info(`Fetching cultural context for locale: ${locale}, entityId: ${entityId}, entityType: ${entityType}`);
        const context = await this.culturalSensitivityRepository.getCulturalContext(locale, entityId, entityType);
        if (!context) {
            throw new errors_1.CustomError('Cultural context not found', { originalErrorCode: 'NotFoundError', originalStatusCode: 404 });
        }
        return context;
    }
    async createOrUpdateCulturalContext(context, entityId, entityType) {
        logger_1.default.info(`Creating or updating cultural context for locale: ${context.locale}, entityId: ${entityId}, entityType: ${entityType}`);
        return this.culturalSensitivityRepository.createOrUpdateCulturalContext(context, entityId, entityType);
    }
    async detectArabicDialect(text) {
        logger_1.default.info(`Detecting Arabic dialect for text: "${text.substring(0, 50)}..."`);
        const detectedDialect = this.mockDialectDetection(text);
        const confidence = 0.95;
        const result = {
            text,
            detectedDialect,
            confidence,
            possibleDialects: [
                { dialect: detectedDialect, confidence: confidence },
                { dialect: 'MSA', confidence: 0.03 },
                { dialect: 'LEV', confidence: 0.02 },
            ],
        };
        await this.culturalSensitivityRepository.saveDialectDetectionResult(result);
        return result;
    }
    async adaptArabicContent(text, targetDialect) {
        logger_1.default.info(`Adapting Arabic content for text: "${text.substring(0, 50)}..." to dialect: ${targetDialect}`);
        const adaptedText = this.mockContentAdaptation(text, targetDialect);
        return { adaptedText };
    }
    tokenizeArabicText(text) {
        return text.split(/\s+/);
    }
    stemArabicText(tokens) {
        return tokens.map(token => token.replace(/^(ال|و|ف|ب|ك|ل|س)/, ''));
    }
    handleDiacritics(text) {
        return text.replace(/[\u064B-\u0652]/g, '');
    }
    processArabicScript(text) {
        return text;
    }
    mockDialectDetection(text) {
        if (text.includes('كيف حالك؟') || text.includes('شو أخبارك؟')) {
            return 'LEV';
        }
        if (text.includes('إزيك؟') || text.includes('عامل إيه؟')) {
            return 'EGY';
        }
        if (text.includes('شلونك؟') || text.includes('اشحالك؟')) {
            return 'SAU';
        }
        if (text.includes('كيفاش؟') || text.includes('واش راك؟')) {
            return 'MAG';
        }
        return 'MSA';
    }
    mockContentAdaptation(text, targetDialect) {
        switch (targetDialect) {
            case 'EGY':
                return text.replace('مرحباً بك', 'أهلاً بيك').replace('كيف حالك؟', 'إزيك؟');
            case 'LEV':
                return text.replace('مرحباً بك', 'أهلاً وسهلاً').replace('كيف حالك؟', 'كيفك؟');
            case 'SAU':
                return text.replace('مرحباً بك', 'يا هلا').replace('كيف حالك؟', 'شلونك؟');
            case 'MAG':
                return text.replace('مرحباً بك', 'أهلاً بيك').replace('كيف حالك؟', 'كيفاش راك؟');
            default:
                return text;
        }
    }
    async getCulturalAppropriatenessScore(content, locale) {
        logger_1.default.info(`Calculating cultural appropriateness score for content: "${content.substring(0, 50)}..." in locale: ${locale}`);
        return content.length > 100 ? 0.5 : 0.9;
    }
    async getContentAdaptationRecommendations(content, locale) {
        logger_1.default.info(`Generating content adaptation recommendations for content: "${content.substring(0, 50)}..." in locale: ${locale}`);
        return ['Consider using more formal language.', 'Avoid direct references to politics.'];
    }
    async validateArabicLanguage(text) {
        logger_1.default.info(`Validating Arabic language for text: "${text.substring(0, 50)}..."`);
        return text.length > 0;
    }
    async enrichCulturalContext(text, locale) {
        logger_1.default.info(`Enriching cultural context for text: "${text.substring(0, 50)}..." in locale: ${locale}`);
        return {
            sentiment: 'neutral',
            namedEntities: ['Dubai', 'UAE'],
            culturalKeywords: ['hospitality', 'tradition'],
        };
    }
}
exports.CulturalSensitivityService = CulturalSensitivityService;
