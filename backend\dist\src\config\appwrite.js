"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthenticationError = exports.NotFoundError = exports.AppwriteError = exports.appwriteService = void 0;
const node_appwrite_1 = require("node-appwrite");
const logger_1 = __importDefault(require("./logger"));
const environment_1 = require("./environment");
class AppwriteService {
    constructor(config) {
        this.config = config;
        this.client = new node_appwrite_1.Client();
        const isPlaceholderConfig = config.projectId.includes('placeholder') ||
            config.databaseId.includes('placeholder');
        if (isPlaceholderConfig) {
            logger_1.default.warn('Appwrite initialized with placeholder configuration - some features may not work', {
                endpoint: config.endpoint,
                projectId: config.projectId,
                databaseId: config.databaseId,
                hasApiKey: !!config.apiKey
            });
        }
        else {
            this.client
                .setEndpoint(config.endpoint)
                .setProject(config.projectId);
            if (config.apiKey) {
                this.client.setKey(config.apiKey);
            }
            logger_1.default.info('Appwrite service initialized', {
                endpoint: config.endpoint,
                projectId: config.projectId,
                databaseId: config.databaseId,
                hasApiKey: !!config.apiKey
            });
        }
        this.databases = new node_appwrite_1.Databases(this.client);
        this.account = new node_appwrite_1.Account(this.client);
        this.users = new node_appwrite_1.Users(this.client);
        this.storage = new node_appwrite_1.Storage(this.client);
    }
    async createDocument(collectionId, documentId, data, permissions) {
        try {
            const result = await this.databases.createDocument(this.config.databaseId, collectionId, documentId, data, permissions);
            logger_1.default.info('Document created successfully', {
                collectionId,
                documentId: result.$id
            });
            return result;
        }
        catch (error) {
            logger_1.default.error('Failed to create document', {
                collectionId,
                documentId,
                error: error.message,
                code: error.code
            });
            throw new AppwriteError('Document creation failed', error);
        }
    }
    async getDocument(collectionId, documentId) {
        try {
            const result = await this.databases.getDocument(this.config.databaseId, collectionId, documentId);
            return result;
        }
        catch (error) {
            if (error.code === 404) {
                throw new NotFoundError(`Document not found: ${documentId}`);
            }
            logger_1.default.error('Failed to get document', {
                collectionId,
                documentId,
                error: error.message,
                code: error.code
            });
            throw new AppwriteError('Document retrieval failed', error);
        }
    }
    async updateDocument(collectionId, documentId, data, permissions) {
        try {
            const result = await this.databases.updateDocument(this.config.databaseId, collectionId, documentId, data, permissions);
            logger_1.default.info('Document updated successfully', {
                collectionId,
                documentId: result.$id
            });
            return result;
        }
        catch (error) {
            logger_1.default.error('Failed to update document', {
                collectionId,
                documentId,
                error: error.message,
                code: error.code
            });
            throw new AppwriteError('Document update failed', error);
        }
    }
    async deleteDocument(collectionId, documentId) {
        try {
            await this.databases.deleteDocument(this.config.databaseId, collectionId, documentId);
            logger_1.default.info('Document deleted successfully', {
                collectionId,
                documentId
            });
        }
        catch (error) {
            logger_1.default.error('Failed to delete document', {
                collectionId,
                documentId,
                error: error.message,
                code: error.code
            });
            throw new AppwriteError('Document deletion failed', error);
        }
    }
    async listDocuments(collectionId, queries, limit, offset) {
        try {
            const result = await this.databases.listDocuments(this.config.databaseId, collectionId);
            return {
                documents: result.documents,
                total: result.total,
            };
        }
        catch (error) {
            logger_1.default.error('Failed to list documents', {
                collectionId,
                error: error.message,
                code: error.code
            });
            throw new AppwriteError('Document listing failed', error);
        }
    }
    async createSession(email, password) {
        try {
            const result = await this.account.createEmailPasswordSession(email, password);
            logger_1.default.info('Session created successfully', {
                userId: result.userId,
                sessionId: result.$id
            });
            return result;
        }
        catch (error) {
            logger_1.default.error('Failed to create session', {
                email,
                error: error.message,
                code: error.code
            });
            throw new AppwriteError('Session creation failed', error);
        }
    }
    async getSession() {
        try {
            const result = await this.account.getSession('current');
            return result;
        }
        catch (error) {
            if (error.code === 401) {
                throw new AuthenticationError('No active session');
            }
            logger_1.default.error('Failed to get session', {
                error: error.message,
                code: error.code
            });
            throw new AppwriteError('Session retrieval failed', error);
        }
    }
    async deleteSession(sessionId) {
        try {
            await this.account.deleteSession(sessionId || 'current');
            logger_1.default.info('Session deleted successfully', { sessionId });
        }
        catch (error) {
            logger_1.default.error('Failed to delete session', {
                sessionId,
                error: error.message,
                code: error.code
            });
            throw new AppwriteError('Session deletion failed', error);
        }
    }
    async healthCheck() {
        try {
            const account = this.getAccount();
            await account.get();
            logger_1.default.info('Appwrite connection successful');
            return true;
        }
        catch (error) {
            logger_1.default.error('Appwrite health check failed', {
                error: error.message,
                code: error.code,
                endpoint: this.config.endpoint,
                projectId: this.config.projectId
            });
            return false;
        }
    }
    async testConnection() {
        try {
            logger_1.default.info('Testing Appwrite connection...', {
                endpoint: this.config.endpoint,
                projectId: this.config.projectId,
                databaseId: this.config.databaseId
            });
            const account = this.getAccount();
            await account.get();
            logger_1.default.info('Appwrite connection test successful');
            return { success: true };
        }
        catch (error) {
            const errorMsg = `Connection failed: ${error.message}`;
            logger_1.default.error('Appwrite connection test failed', {
                error: error.message,
                code: error.code,
                type: error.type,
                endpoint: this.config.endpoint,
                projectId: this.config.projectId
            });
            return { success: false, error: errorMsg };
        }
    }
    createQuery() {
        return {
            equal: (attribute, value) => node_appwrite_1.Query.equal(attribute, value),
            notEqual: (attribute, value) => node_appwrite_1.Query.notEqual(attribute, value),
            lessThan: (attribute, value) => node_appwrite_1.Query.lessThan(attribute, value),
            greaterThan: (attribute, value) => node_appwrite_1.Query.greaterThan(attribute, value),
            search: (attribute, value) => node_appwrite_1.Query.search(attribute, value),
            orderAsc: (attribute) => node_appwrite_1.Query.orderAsc(attribute),
            orderDesc: (attribute) => node_appwrite_1.Query.orderDesc(attribute),
            limit: (limit) => node_appwrite_1.Query.limit(limit),
            offset: (offset) => node_appwrite_1.Query.offset(offset),
        };
    }
    getClient() {
        return this.client;
    }
    getAccount() {
        return this.account;
    }
    getDatabases() {
        return this.databases;
    }
    getStorage() {
        return this.storage;
    }
    getUsers() {
        return this.users;
    }
}
class AppwriteError extends Error {
    constructor(message, originalError) {
        super(message);
        this.originalError = originalError;
        this.name = 'AppwriteError';
    }
}
exports.AppwriteError = AppwriteError;
class NotFoundError extends Error {
    constructor(message) {
        super(message);
        this.name = 'NotFoundError';
    }
}
exports.NotFoundError = NotFoundError;
class AuthenticationError extends Error {
    constructor(message) {
        super(message);
        this.name = 'AuthenticationError';
    }
}
exports.AuthenticationError = AuthenticationError;
const envConfig = (0, environment_1.validateEnvironment)();
const appwriteConfig = envConfig.appwrite;
exports.appwriteService = new AppwriteService(appwriteConfig);
