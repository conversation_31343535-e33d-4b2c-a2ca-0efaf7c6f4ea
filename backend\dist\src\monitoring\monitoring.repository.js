"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MonitoringRepository = void 0;
const db_1 = __importDefault(require("../database/db"));
class MonitoringRepository {
    constructor() {
        this.SYSTEM_HEALTH_TABLE = 'system_health';
        this.PERFORMANCE_METRICS_TABLE = 'performance_metrics';
        this.SYSTEM_ERRORS_TABLE = 'system_errors';
        this.COVE_ESCALATIONS_TABLE = 'cove_escalations';
        this.MONITORING_ALERTS_TABLE = 'monitoring_alerts';
        this.RECOVERY_ACTIONS_TABLE = 'recovery_actions';
        this.AUDIT_LOGS_TABLE = 'audit_logs';
    }
    async addSystemHealth(health) {
        const [newHealth] = await (0, db_1.default)(this.SYSTEM_HEALTH_TABLE).insert(health).returning('*');
        return newHealth;
    }
    async getLatestSystemHealth(serviceName) {
        return (0, db_1.default)(this.SYSTEM_HEALTH_TABLE)
            .where({ serviceName })
            .orderBy('timestamp', 'desc')
            .first();
    }
    async addPerformanceMetrics(metrics) {
        const [newMetrics] = await (0, db_1.default)(this.PERFORMANCE_METRICS_TABLE).insert(metrics).returning('*');
        return newMetrics;
    }
    async getPerformanceMetrics(serviceName, startTime, endTime) {
        return (0, db_1.default)(this.PERFORMANCE_METRICS_TABLE)
            .where({ serviceName })
            .whereBetween('timestamp', [startTime, endTime])
            .orderBy('timestamp', 'asc');
    }
    async addSystemError(error) {
        const [newError] = await (0, db_1.default)(this.SYSTEM_ERRORS_TABLE).insert(error).returning('*');
        return newError;
    }
    async updateSystemError(id, updates) {
        const [updatedError] = await (0, db_1.default)(this.SYSTEM_ERRORS_TABLE).where({ id }).update(updates).returning('*');
        return updatedError;
    }
    async getSystemError(id) {
        return (0, db_1.default)(this.SYSTEM_ERRORS_TABLE).where({ id }).first();
    }
    async getUnresolvedSystemErrors() {
        return (0, db_1.default)(this.SYSTEM_ERRORS_TABLE).where({ isResolved: false });
    }
    async addCoveEscalation(escalation) {
        const [newEscalation] = await (0, db_1.default)(this.COVE_ESCALATIONS_TABLE).insert(escalation).returning('*');
        return newEscalation;
    }
    async updateCoveEscalation(id, updates) {
        const [updatedEscalation] = await (0, db_1.default)(this.COVE_ESCALATIONS_TABLE).where({ id }).update(updates).returning('*');
        return updatedEscalation;
    }
    async getCoveEscalation(id) {
        return (0, db_1.default)(this.COVE_ESCALATIONS_TABLE).where({ id }).first();
    }
    async addMonitoringAlert(alert) {
        const [newAlert] = await (0, db_1.default)(this.MONITORING_ALERTS_TABLE).insert(alert).returning('*');
        return newAlert;
    }
    async updateMonitoringAlert(id, updates) {
        const [updatedAlert] = await (0, db_1.default)(this.MONITORING_ALERTS_TABLE).where({ id }).update(updates).returning('*');
        return updatedAlert;
    }
    async getMonitoringAlert(id) {
        return (0, db_1.default)(this.MONITORING_ALERTS_TABLE).where({ id }).first();
    }
    async getActiveMonitoringAlerts() {
        return (0, db_1.default)(this.MONITORING_ALERTS_TABLE).where({ isAcknowledged: false });
    }
    async addRecoveryAction(action) {
        const [newAction] = await (0, db_1.default)(this.RECOVERY_ACTIONS_TABLE).insert(action).returning('*');
        return newAction;
    }
    async updateRecoveryAction(id, updates) {
        const [updatedAction] = await (0, db_1.default)(this.RECOVERY_ACTIONS_TABLE).where({ id }).update(updates).returning('*');
        return updatedAction;
    }
    async getRecoveryAction(id) {
        return (0, db_1.default)(this.RECOVERY_ACTIONS_TABLE).where({ id }).first();
    }
    async addAuditLog(log) {
        const [newLog] = await (0, db_1.default)(this.AUDIT_LOGS_TABLE).insert(log).returning('*');
        return newLog;
    }
    async getAuditLogs(eventType, entityType, entityId) {
        const query = (0, db_1.default)(this.AUDIT_LOGS_TABLE);
        if (eventType)
            query.where({ eventType });
        if (entityType)
            query.where({ entityType });
        if (entityId)
            query.where({ entityId });
        return query.orderBy('timestamp', 'desc');
    }
}
exports.MonitoringRepository = MonitoringRepository;
