"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthService = void 0;
const appwrite_adapter_1 = require("../database/adapters/appwrite-adapter");
const auth_utils_1 = require("./auth.utils");
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const logger_1 = __importDefault(require("../config/logger"));
const errors_1 = require("../utils/errors");
class AuthService {
    constructor(databaseAdapter) {
        this.db = databaseAdapter || new appwrite_adapter_1.AppwriteAdapter();
    }
    async register(userData) {
        try {
            if (!userData.email || !userData.password) {
                throw new errors_1.ValidationError('Email and password are required');
            }
            const existingUser = await this.db.getUserByEmail(userData.email);
            if (existingUser) {
                throw new errors_1.ValidationError('User already exists with this email');
            }
            const user = await this.db.createUser(userData);
            const tokens = await this.generateTokens(user);
            await this.db.createSession(user.user_id, {
                loginTime: new Date().toISOString(),
                userAgent: 'backend-service'
            });
            logger_1.default.info('User registered successfully', {
                userId: user.user_id,
                email: user.email
            });
            return { user: this.sanitizeUser(user), tokens };
        }
        catch (error) {
            logger_1.default.error('User registration failed', {
                email: userData.email,
                error: error.message
            });
            throw error;
        }
    }
    async login(credentials) {
        try {
            if (!credentials.email || !credentials.password) {
                throw new errors_1.ValidationError('Email and password are required');
            }
            const user = await this.db.getUserByEmail(credentials.email);
            if (!user) {
                throw new errors_1.AuthenticationError('Invalid credentials');
            }
            const isValidPassword = await bcryptjs_1.default.compare(credentials.password, user.password_hash || '');
            if (!isValidPassword) {
                throw new errors_1.AuthenticationError('Invalid credentials');
            }
            const tokens = await this.generateTokens(user);
            await this.db.createSession(user.user_id, {
                loginTime: new Date().toISOString(),
                userAgent: 'backend-service'
            });
            logger_1.default.info('User logged in successfully', {
                userId: user.user_id,
                email: user.email
            });
            return { user: this.sanitizeUser(user), tokens };
        }
        catch (error) {
            logger_1.default.error('User login failed', {
                email: credentials.email,
                error: error.message
            });
            throw error;
        }
    }
    async verifyToken(token) {
        try {
            const payload = (0, auth_utils_1.verifyToken)(token);
            const user = await this.db.getUserById(payload.userId);
            if (!user) {
                throw new errors_1.AuthenticationError('User not found');
            }
            return payload;
        }
        catch (error) {
            logger_1.default.error('Token verification failed', { error: error.message });
            throw new errors_1.AuthenticationError('Invalid token');
        }
    }
    async refreshToken(refreshToken) {
        try {
            const payload = (0, auth_utils_1.verifyToken)(refreshToken);
            const user = await this.db.getUserById(payload.userId);
            if (!user) {
                throw new errors_1.AuthenticationError('User not found');
            }
            return this.generateTokens(user);
        }
        catch (error) {
            logger_1.default.error('Token refresh failed', { error: error.message });
            throw new errors_1.AuthenticationError('Invalid refresh token');
        }
    }
    async logout(userId, sessionId) {
        try {
            if (sessionId) {
                await this.db.deleteSession(sessionId);
            }
            logger_1.default.info('User logged out successfully', { userId });
        }
        catch (error) {
            logger_1.default.error('Logout failed', { userId, error: error.message });
            throw error;
        }
    }
    async healthCheck() {
        return this.db.healthCheck();
    }
    async generateTokens(user) {
        const payload = {
            userId: user.user_id,
            tenantId: user.tenant_id || '',
            email: user.email,
            paimTier: user.paim_tier || 'basic',
            roles: user.roles || ['user'],
        };
        const { accessToken, refreshToken, expiresIn } = (0, auth_utils_1.generateTokens)(user, payload.tenantId, payload.roles);
        return {
            accessToken,
            refreshToken,
            expiresIn,
        };
    }
    sanitizeUser(user) {
        const { password, ...sanitizedUser } = user;
        return sanitizedUser;
    }
}
exports.AuthService = AuthService;
