"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.requireEnvVar = requireEnvVar;
exports.getEnvVar = getEnvVar;
exports.requireParam = requireParam;
exports.getParam = getParam;
exports.isValidString = isValidString;
exports.assertString = assertString;
function requireEnvVar(value, name) {
    if (!value) {
        throw new Error(`Required environment variable ${name} is not set`);
    }
    return value;
}
function getEnvVar(value, defaultValue = '') {
    return value ?? defaultValue;
}
function requireParam(value, name) {
    if (!value) {
        throw new Error(`Required parameter ${name} is missing`);
    }
    return value;
}
function getParam(value, defaultValue = '') {
    return value ?? defaultValue;
}
function isValidString(value) {
    return typeof value === 'string' && value.length > 0;
}
function assertString(value, name) {
    if (!isValidString(value)) {
        throw new Error(`Expected ${name} to be a non-empty string, got: ${typeof value}`);
    }
}
