"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailService = void 0;
const logger_1 = __importDefault(require("../config/logger"));
class EmailService {
    async sendEmail(to, subject, body) {
        logger_1.default.info(`Attempting to send email to: ${to}`);
        logger_1.default.info(`Subject: ${subject}`);
        logger_1.default.info(`Body: ${body}`);
        console.log(`Simulating email sent to ${to} with subject "${subject}"`);
    }
}
exports.EmailService = EmailService;
