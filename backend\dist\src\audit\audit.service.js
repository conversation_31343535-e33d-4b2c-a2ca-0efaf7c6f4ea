"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuditTrailService = void 0;
const audit_repository_1 = require("./audit.repository");
const audit_types_1 = require("./audit.types");
const authorization_service_1 = require("../auth/authorization.service");
const permissions_1 = require("../auth/permissions");
const errors_1 = require("../utils/errors");
class AuditTrailService {
    constructor() {
        this.auditRepository = new audit_repository_1.AuditRepository();
    }
    async logEvent(event) {
        try {
            if (!event.timestamp) {
                event.timestamp = new Date();
            }
            await this.auditRepository.saveAuditEvent(event);
            console.log(`Audit Logged: Category: ${event.category}, Operation: ${event.operationType}, Severity: ${event.severity}, Tenant: ${event.tenantId}, User: ${event.userId || 'N/A'}`);
        }
        catch (error) {
            console.error('Failed to log audit event:', error);
        }
    }
    async getAuditLogs(user, filters, page = 1, limit = 20) {
        if (!authorization_service_1.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.AUDIT_VIEW)) {
            throw new errors_1.AuthorizationError('Forbidden: Insufficient permissions to view audit logs.', 403);
        }
        if (!authorization_service_1.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.SYSTEM_VIEW_LOGS)) {
            filters.tenantId = user.tenantId;
        }
        return this.auditRepository.getAuditEvents(filters, page, limit);
    }
    async generateComplianceReport(user, complianceStandard, startDate, endDate) {
        if (!authorization_service_1.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.AUDIT_VIEW)) {
            throw new errors_1.AuthorizationError('Forbidden: Insufficient permissions to generate compliance report.', 403);
        }
        return this.auditRepository.getComplianceReport(user.tenantId, complianceStandard, startDate, endDate);
    }
    async getAuditAnalytics(user, startDate, endDate) {
        if (!authorization_service_1.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.AUDIT_VIEW)) {
            throw new errors_1.AuthorizationError('Forbidden: Insufficient permissions to view audit analytics.', 403);
        }
        return this.auditRepository.getAuditAnalytics(user.tenantId, startDate, endDate);
    }
    async applyDataRetentionPolicy(user, retentionDays) {
        if (!authorization_service_1.authorizationService.hasPermission(user.roles, user.paimTier, permissions_1.PERMISSIONS.AUDIT_MANAGE)) {
            throw new errors_1.AuthorizationError('Forbidden: Insufficient permissions to apply data retention policy.', 403);
        }
        console.log(`Applying data retention policy for tenant ${user.tenantId}: retaining data for ${retentionDays} days.`);
        await this.logEvent({
            tenantId: user.tenantId,
            category: audit_types_1.AuditEventCategory.COMPLIANCE_EVENT,
            operationType: 'DATA_RETENTION_POLICY_APPLIED',
            description: `Data older than ${retentionDays} days marked for archival/deletion.`,
            severity: audit_types_1.AuditEventSeverity.INFO,
            timestamp: new Date(),
            metadata: { retentionDays },
        });
    }
    async archiveOldData(tenantId, archiveBeforeDate) {
        console.log(`Archiving data for tenant ${tenantId} before ${archiveBeforeDate.toISOString()}.`);
        await this.logEvent({
            tenantId: tenantId,
            category: audit_types_1.AuditEventCategory.COMPLIANCE_EVENT,
            operationType: 'DATA_ARCHIVAL_INITIATED',
            description: `Data archival initiated for tenant ${tenantId}. Data before ${archiveBeforeDate.toISOString()} is being archived.`,
            severity: audit_types_1.AuditEventSeverity.INFO,
            timestamp: new Date(),
            metadata: { archiveBeforeDate },
        });
    }
}
exports.AuditTrailService = AuditTrailService;
