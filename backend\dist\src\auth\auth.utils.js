"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyToken = exports.generateTokens = exports.comparePassword = exports.hashPassword = void 0;
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const JWT_SECRET = process.env.JWT_SECRET || 'supersecretjwtkey';
const JWT_ACCESS_TOKEN_EXPIRATION = process.env.JWT_ACCESS_TOKEN_EXPIRATION || '15m';
const JWT_REFRESH_TOKEN_EXPIRATION = process.env.JWT_REFRESH_TOKEN_EXPIRATION || '7d';
const hashPassword = async (password) => {
    const saltRounds = 10;
    return bcryptjs_1.default.hash(password, saltRounds);
};
exports.hashPassword = hashPassword;
const comparePassword = async (password, hash) => {
    return bcryptjs_1.default.compare(password, hash);
};
exports.comparePassword = comparePassword;
const generateTokens = (user, tenantId, roles) => {
    const payload = {
        userId: user.user_id,
        tenantId: tenantId,
        email: user.email,
        paimTier: user.paim_tier,
        roles: roles,
    };
    const accessToken = jsonwebtoken_1.default.sign(payload, JWT_SECRET, { expiresIn: JWT_ACCESS_TOKEN_EXPIRATION });
    const refreshToken = jsonwebtoken_1.default.sign(payload, JWT_SECRET, { expiresIn: JWT_REFRESH_TOKEN_EXPIRATION });
    const parseDurationToSeconds = (duration) => {
        const value = parseInt(duration);
        if (duration.includes('m')) {
            return value * 60;
        }
        else if (duration.includes('h')) {
            return value * 3600;
        }
        else if (duration.includes('d')) {
            return value * 86400;
        }
        return value;
    };
    return {
        accessToken,
        refreshToken,
        expiresIn: parseDurationToSeconds(JWT_ACCESS_TOKEN_EXPIRATION),
    };
};
exports.generateTokens = generateTokens;
const verifyToken = (token) => {
    try {
        return jsonwebtoken_1.default.verify(token, JWT_SECRET);
    }
    catch (error) {
        console.error('Token verification failed:', error);
        return null;
    }
};
exports.verifyToken = verifyToken;
